"""
体力值管理系统服务
根据PRD需求实现完整的体力值管理、恢复和广告系统
"""
import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.orm import selectinload

from app.core.database import get_db
from app.core.redis_client import redis_manager
from app.models.user import User

logger = logging.getLogger(__name__)


class StaminaManagementService:
    """体力值管理系统服务"""
    
    def __init__(self):
        self.max_stamina = 120  # PRD规定最大体力120点
        self.natural_recovery_interval = 180  # 3分钟恢复1点体力
        self.ad_recovery_amount = 30  # 广告恢复30点体力
        self.max_ad_per_hour = 3  # 每小时最多观看3次广告
        self.ad_cooldown = 1200  # 20分钟冷却时间
        
        # 体力消耗配置（根据PRD新要求）
        self.stamina_costs = {
            "catch_thief": 0.5,    # PRD新要求：抓捕小偷0.5点体力
            "clean_rubbish": 0.3,  # PRD新要求：清理垃圾0.3点体力
            "monument_quiz": 5     # 古迹问答：5点体力
        }
    
    # ==================== 体力值管理 ====================
    
    async def get_user_stamina_info(self, user_id: int) -> Dict[str, Any]:
        """获取用户体力信息"""
        try:
            async for db in get_db():
                user = await self._get_user(db, user_id)
                if not user:
                    return {"error": "用户不存在", "code": "USER_NOT_FOUND"}
                
                # 自然恢复体力
                await self._natural_stamina_recovery(user)
                await db.commit()
                
                # 计算下次恢复时间
                next_recovery_time = self._calculate_next_recovery_time(user)
                
                # 检查广告恢复状态
                ad_info = await self._check_ad_recovery_status(user)
                
                return {
                    "success": True,
                    "current_stamina": user.stamina,
                    "max_stamina": user.max_stamina,
                    "stamina_percentage": round((user.stamina / user.max_stamina) * 100, 2),
                    "experience_efficiency": user.get_experience_efficiency(),
                    "is_low_stamina": user.stamina < 30,
                    "is_critical_stamina": user.stamina < 10,
                    "recovery_info": {
                        "next_recovery_in_seconds": next_recovery_time,
                        "recovery_rate": "1点/3分钟",
                        "time_to_full": self._calculate_time_to_full_stamina(user)
                    },
                    "ad_recovery": ad_info,
                    "stamina_costs": self.stamina_costs
                }
                
        except Exception as e:
            logger.error(f"获取用户体力信息失败 user_id={user_id}: {e}")
            return {"error": "获取体力信息失败", "code": "STAMINA_INFO_ERROR"}
    
    async def add_stamina(
        self,
        user_id: int,
        amount: int,
        source: str,
        description: str = None
    ) -> Dict[str, Any]:
        """添加体力值（用于奖励等）"""
        try:
            async for db in get_db():
                user = await self._get_user(db, user_id)
                if not user:
                    return {"success": False, "message": "用户不存在"}
                
                # 自然恢复体力
                await self._natural_stamina_recovery(user)
                
                # 添加体力，不超过最大值
                old_stamina = user.stamina
                user.stamina = min(user.max_stamina, user.stamina + amount)
                actual_added = user.stamina - old_stamina
                
                # 保存到数据库
                await db.commit()
                
                # 记录日志
                await self._log_stamina_change(
                    user.id,
                    actual_added,
                    source,
                    {"description": description or f"获得{amount}点体力值"}
                )
                
                logger.info(f"用户 {user_id} 通过 {source} 获得 {actual_added} 点体力值")
                
                return {
                    "success": True,
                    "message": f"获得 {actual_added} 点体力值",
                    "added_stamina": actual_added,
                    "current_stamina": user.stamina,
                    "max_stamina": user.max_stamina
                }
                
        except Exception as e:
            logger.error(f"添加体力失败 user_id={user_id}, amount={amount}: {e}")
            return {"success": False, "message": "添加体力失败，请稍后重试"}
    
    async def consume_stamina(
        self, 
        user_id: int, 
        action: str, 
        amount: int = 1,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """消耗体力"""
        try:
            cost_per_action = self.stamina_costs.get(action, 1)
            total_cost = cost_per_action * amount
            
            async for db in get_db():
                user = await self._get_user(db, user_id)
                if not user:
                    return {"error": "用户不存在", "code": "USER_NOT_FOUND"}
                
                # 自然恢复体力
                await self._natural_stamina_recovery(user)
                
                # 检查体力是否足够
                if user.stamina < total_cost:
                    # 检查是否可以观看广告恢复
                    ad_info = await self._check_ad_recovery_status(user)
                    
                    return {
                        "error": "体力不足",
                        "code": "INSUFFICIENT_STAMINA",
                        "current_stamina": user.stamina,
                        "required_stamina": total_cost,
                        "shortage": total_cost - user.stamina,
                        "ad_recovery_available": ad_info["can_watch_ad"],
                        "ad_recovery_amount": self.ad_recovery_amount,
                        "next_ad_available_in": ad_info.get("next_available_in_seconds", 0),
                        "natural_recovery_time": self._calculate_time_to_stamina(user, total_cost)
                    }
                
                # 消耗体力
                user.stamina -= total_cost
                user.last_stamina_update = datetime.utcnow()
                
                await db.commit()
                
                # 记录体力消耗日志
                await self._log_stamina_change(
                    user_id, 
                    -total_cost, 
                    f"consume_{action}", 
                    {**(context or {}), "action": action, "amount": amount}
                )
                
                # 更新缓存
                await self._update_stamina_cache(user)
                
                return {
                    "success": True,
                    "stamina_consumed": total_cost,
                    "current_stamina": user.stamina,
                    "experience_efficiency": user.get_experience_efficiency(),
                    "low_stamina_warning": user.stamina < 30,
                    "critical_stamina_warning": user.stamina < 10,
                    "time_to_recovery": self._calculate_next_recovery_time(user)
                }
                
        except Exception as e:
            logger.error(f"消耗体力失败 user_id={user_id}, action={action}: {e}")
            return {"error": "消耗体力失败", "code": "STAMINA_CONSUME_ERROR"}
    
    # ==================== 自然恢复系统 ====================
    
    async def _natural_stamina_recovery(self, user: User) -> int:
        """自然恢复体力"""
        if not user.last_stamina_update:
            user.last_stamina_update = datetime.utcnow()
            return 0
        
        now = datetime.utcnow()
        time_diff = (now - user.last_stamina_update).total_seconds()
        recovery_points = int(time_diff // self.natural_recovery_interval)
        
        if recovery_points > 0 and user.stamina < user.max_stamina:
            old_stamina = user.stamina
            user.stamina = min(user.max_stamina, user.stamina + recovery_points)
            user.last_stamina_update = now
            
            recovered = user.stamina - old_stamina
            if recovered > 0:
                # 记录自然恢复日志
                await self._log_stamina_change(
                    user.id, 
                    recovered, 
                    "natural_recovery",
                    {"recovery_points": recovery_points, "time_diff_seconds": int(time_diff)}
                )
            
            return recovered
        
        return 0
    
    def _calculate_next_recovery_time(self, user: User) -> int:
        """计算下次恢复体力的时间（秒）"""
        if user.stamina >= user.max_stamina:
            return 0
        
        if not user.last_stamina_update:
            return self.natural_recovery_interval
        
        now = datetime.utcnow()
        time_since_update = (now - user.last_stamina_update).total_seconds()
        time_to_next = self.natural_recovery_interval - (time_since_update % self.natural_recovery_interval)
        
        return int(time_to_next)
    
    def _calculate_time_to_full_stamina(self, user: User) -> int:
        """计算恢复满体力的时间（秒）"""
        if user.stamina >= user.max_stamina:
            return 0
        
        stamina_needed = user.max_stamina - user.stamina
        time_for_missing = stamina_needed * self.natural_recovery_interval
        
        # 减去已经恢复的时间
        next_recovery_time = self._calculate_next_recovery_time(user)
        
        return time_for_missing - (self.natural_recovery_interval - next_recovery_time)
    
    def _calculate_time_to_stamina(self, user: User, target_stamina: int) -> int:
        """计算恢复到指定体力值的时间（秒）"""
        if user.stamina >= target_stamina:
            return 0
        
        stamina_needed = target_stamina - user.stamina
        time_needed = stamina_needed * self.natural_recovery_interval
        
        # 减去已经恢复的时间
        next_recovery_time = self._calculate_next_recovery_time(user)
        
        return max(0, time_needed - (self.natural_recovery_interval - next_recovery_time))
    
    # ==================== 广告恢复系统 ====================
    
    async def _check_ad_recovery_status(self, user: User) -> Dict[str, Any]:
        """检查广告恢复状态"""
        now = datetime.utcnow()
        
        # 检查今日观看次数
        today_count = await self._get_today_ad_count(user.id)
        
        # 检查冷却时间
        can_watch = True
        next_available_in = 0
        
        if user.last_stamina_ad_time:
            time_since_last = (now - user.last_stamina_ad_time).total_seconds()
            if time_since_last < self.ad_cooldown:
                can_watch = False
                next_available_in = int(self.ad_cooldown - time_since_last)
        
        # 检查每小时限制
        if today_count >= self.max_ad_per_hour * 24:  # 每天最多72次（24小时*3次）
            can_watch = False
        
        return {
            "can_watch_ad": can_watch and user.stamina < user.max_stamina,
            "today_ad_count": today_count,
            "max_daily_ads": self.max_ad_per_hour * 24,
            "next_available_in_seconds": next_available_in,
            "recovery_amount": self.ad_recovery_amount,
            "current_cooldown_minutes": self.ad_cooldown // 60
        }
    
    async def watch_stamina_ad(self, user_id: int) -> Dict[str, Any]:
        """观看体力恢复广告"""
        try:
            async for db in get_db():
                user = await self._get_user(db, user_id)
                if not user:
                    return {"error": "用户不存在", "code": "USER_NOT_FOUND"}
                
                # 检查是否可以观看广告
                ad_info = await self._check_ad_recovery_status(user)
                if not ad_info["can_watch_ad"]:
                    return {
                        "error": "无法观看广告",
                        "code": "AD_NOT_AVAILABLE",
                        "reason": "冷却中或已达上限",
                        "ad_info": ad_info
                    }
                
                # 自然恢复体力（观看广告前）
                await self._natural_stamina_recovery(user)
                
                # 广告恢复体力
                old_stamina = user.stamina
                user.stamina = min(user.max_stamina, user.stamina + self.ad_recovery_amount)
                user.last_stamina_ad_time = datetime.utcnow()
                user.stamina_ads_watched += 1
                user.total_ads_watched += 1
                
                recovered = user.stamina - old_stamina
                
                await db.commit()
                
                # 记录广告恢复日志
                await self._log_stamina_change(
                    user_id,
                    recovered,
                    "ad_recovery",
                    {
                        "ad_recovery_amount": self.ad_recovery_amount,
                        "actual_recovered": recovered,
                        "total_ads_watched": user.total_ads_watched
                    }
                )
                
                # 更新缓存
                await self._update_stamina_cache(user)
                
                # 更新今日广告计数
                await self._increment_today_ad_count(user_id)
                
                return {
                    "success": True,
                    "stamina_recovered": recovered,
                    "current_stamina": user.stamina,
                    "ad_count_today": await self._get_today_ad_count(user_id),
                    "next_ad_available_in": self.ad_cooldown,
                    "experience_efficiency": user.get_experience_efficiency()
                }
                
        except Exception as e:
            logger.error(f"观看体力广告失败 user_id={user_id}: {e}")
            return {"error": "观看广告失败", "code": "AD_RECOVERY_ERROR"}
    
    async def _get_today_ad_count(self, user_id: int) -> int:
        """获取今日广告观看次数"""
        try:
            today_key = f"user:{user_id}:stamina_ads:{datetime.now().strftime('%Y%m%d')}"
            count = await redis_manager.get(today_key)
            return int(count) if count else 0
        except Exception:
            return 0
    
    async def _increment_today_ad_count(self, user_id: int):
        """增加今日广告计数"""
        try:
            today_key = f"user:{user_id}:stamina_ads:{datetime.now().strftime('%Y%m%d')}"
            await redis_manager.incr(today_key)
            await redis_manager.expire(today_key, 86400)  # 24小时过期
        except Exception as e:
            logger.error(f"更新广告计数失败 user_id={user_id}: {e}")
    
    # ==================== 特殊恢复系统 ====================
    
    async def special_stamina_recovery(
        self, 
        user_id: int, 
        amount: int, 
        reason: str,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """特殊体力恢复（如叛变小偷、升级奖励等）"""
        try:
            async for db in get_db():
                user = await self._get_user(db, user_id)
                if not user:
                    return {"error": "用户不存在", "code": "USER_NOT_FOUND"}
                
                old_stamina = user.stamina
                user.stamina = min(user.max_stamina, user.stamina + amount)
                user.last_stamina_update = datetime.utcnow()
                
                recovered = user.stamina - old_stamina
                
                await db.commit()
                
                # 记录特殊恢复日志
                await self._log_stamina_change(
                    user_id,
                    recovered,
                    f"special_{reason}",
                    {**(context or {}), "reason": reason, "requested_amount": amount}
                )
                
                # 更新缓存
                await self._update_stamina_cache(user)
                
                return {
                    "success": True,
                    "stamina_recovered": recovered,
                    "current_stamina": user.stamina,
                    "recovery_reason": reason,
                    "experience_efficiency": user.get_experience_efficiency()
                }
                
        except Exception as e:
            logger.error(f"特殊体力恢复失败 user_id={user_id}, reason={reason}: {e}")
            return {"error": "特殊恢复失败", "code": "SPECIAL_RECOVERY_ERROR"}
    
    async def rebel_thief_recovery(self, user_id: int) -> Dict[str, Any]:
        """叛变小偷体力恢复（PRD规定10%概率遇到，恢复10点体力）"""
        return await self.special_stamina_recovery(
            user_id, 
            10, 
            "rebel_thief",
            {"recovery_type": "rebel_thief_bonus", "probability": "10%"}
        )
    
    async def level_up_recovery(self, user_id: int) -> Dict[str, Any]:
        """升级完全恢复体力（PRD规定升级时完全恢复）"""
        try:
            async for db in get_db():
                user = await self._get_user(db, user_id)
                if not user:
                    return {"error": "用户不存在", "code": "USER_NOT_FOUND"}
                
                old_stamina = user.stamina
                user.stamina = user.max_stamina
                user.last_stamina_update = datetime.utcnow()
                
                recovered = user.stamina - old_stamina
                
                await db.commit()
                
                # 记录升级恢复日志
                await self._log_stamina_change(
                    user_id,
                    recovered,
                    "level_up_recovery",
                    {
                        "recovery_type": "level_up_full_recovery",
                        "guardian_level": user.guardian_level
                    }
                )
                
                # 更新缓存
                await self._update_stamina_cache(user)
                
                return {
                    "success": True,
                    "stamina_recovered": recovered,
                    "current_stamina": user.stamina,
                    "recovery_type": "level_up_full_recovery"
                }
                
        except Exception as e:
            logger.error(f"升级体力恢复失败 user_id={user_id}: {e}")
            return {"error": "升级恢复失败", "code": "LEVEL_UP_RECOVERY_ERROR"}
    
    # ==================== 道具恢复系统 ====================
    
    async def use_stamina_potion(self, user_id: int) -> Dict[str, Any]:
        """使用体力药水（立即恢复20体力）"""
        try:
            async for db in get_db():
                user = await self._get_user(db, user_id)
                if not user:
                    return {"error": "用户不存在", "code": "USER_NOT_FOUND"}
                
                # 检查是否有体力药水
                if user.stamina_potion_count <= 0:
                    return {
                        "error": "没有体力药水",
                        "code": "NO_STAMINA_POTION",
                        "current_potions": user.stamina_potion_count
                    }
                
                # 使用药水
                user.stamina_potion_count -= 1
                old_stamina = user.stamina
                user.stamina = min(user.max_stamina, user.stamina + 20)
                user.last_stamina_update = datetime.utcnow()
                
                recovered = user.stamina - old_stamina
                
                await db.commit()
                
                # 记录药水使用日志
                await self._log_stamina_change(
                    user_id,
                    recovered,
                    "stamina_potion",
                    {
                        "potion_recovery": 20,
                        "actual_recovered": recovered,
                        "remaining_potions": user.stamina_potion_count
                    }
                )
                
                # 更新缓存
                await self._update_stamina_cache(user)
                
                return {
                    "success": True,
                    "stamina_recovered": recovered,
                    "current_stamina": user.stamina,
                    "remaining_potions": user.stamina_potion_count,
                    "potion_recovery_amount": 20
                }
                
        except Exception as e:
            logger.error(f"使用体力药水失败 user_id={user_id}: {e}")
            return {"error": "使用药水失败", "code": "POTION_USE_ERROR"}
    
    # ==================== 辅助方法 ====================
    
    async def _get_user(self, db: AsyncSession, user_id: int) -> Optional[User]:
        """获取用户信息"""
        result = await db.execute(
            select(User).where(User.id == user_id)
        )
        return result.scalar_one_or_none()
    
    async def _log_stamina_change(
        self, 
        user_id: int, 
        change_amount: int, 
        change_type: str,
        context: Dict[str, Any] = None
    ):
        """记录体力变化日志"""
        try:
            log_entry = {
                "user_id": user_id,
                "change_amount": change_amount,
                "change_type": change_type,
                "context": context or {},
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # 存储到Redis列表中，用于统计和分析
            await redis_manager.lpush(
                f"user:{user_id}:stamina_logs",
                str(log_entry)
            )
            await redis_manager.ltrim(f"user:{user_id}:stamina_logs", 0, 99)  # 保留最近100条
            await redis_manager.expire(f"user:{user_id}:stamina_logs", 86400 * 7)  # 7天过期
            
        except Exception as e:
            logger.error(f"记录体力变化日志失败 user_id={user_id}: {e}")
    
    async def _update_stamina_cache(self, user: User):
        """更新体力缓存"""
        try:
            stamina_info = {
                "current_stamina": user.stamina,
                "max_stamina": user.max_stamina,
                "last_update": user.last_stamina_update.isoformat() if user.last_stamina_update else None,
                "experience_efficiency": user.get_experience_efficiency(),
                "updated_at": datetime.utcnow().isoformat()
            }
            
            await redis_manager.hmset(
                f"user:{user.id}:stamina_info",
                stamina_info
            )
            await redis_manager.expire(f"user:{user.id}:stamina_info", 3600)  # 1小时过期
            
        except Exception as e:
            logger.error(f"更新体力缓存失败 user_id={user.id}: {e}")
    
    # ==================== 批量操作 ====================
    
    async def batch_natural_recovery(self, user_ids: List[int]) -> Dict[str, Any]:
        """批量处理自然体力恢复"""
        try:
            recovered_users = []
            failed_users = []
            
            async for db in get_db():
                for user_id in user_ids:
                    try:
                        user = await self._get_user(db, user_id)
                        if user:
                            recovery_amount = await self._natural_stamina_recovery(user)
                            if recovery_amount > 0:
                                recovered_users.append({
                                    "user_id": user_id,
                                    "recovered": recovery_amount,
                                    "current_stamina": user.stamina
                                })
                                await self._update_stamina_cache(user)
                    except Exception as e:
                        failed_users.append({"user_id": user_id, "error": str(e)})
                
                await db.commit()
                break
            
            return {
                "success": True,
                "recovered_count": len(recovered_users),
                "failed_count": len(failed_users),
                "recovered_users": recovered_users,
                "failed_users": failed_users
            }
            
        except Exception as e:
            logger.error(f"批量体力恢复失败: {e}")
            return {"error": "批量恢复失败", "code": "BATCH_RECOVERY_ERROR"}


# 全局实例
stamina_service = StaminaManagementService()