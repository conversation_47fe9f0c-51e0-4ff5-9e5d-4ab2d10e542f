"""
幸运转盘服务
提供转盘抽奖功能，奖励守护经验值或体力值
"""
import asyncio
import json
import random
from datetime import datetime, time, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import text
from app.core.redis_client import redis_manager
from app.models.user import User
from app.services.guardian_experience_service import GuardianExperienceService
from app.services.stamina_management_service import StaminaManagementService
import logging

logger = logging.getLogger(__name__)

class LotteryService:
    """幸运转盘服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.guardian_service = GuardianExperienceService()
        self.stamina_service = StaminaManagementService()
        
        # 转盘奖品配置 - 按12点方向开始顺时针排列
        self.default_prizes = [
            {"id": 1, "icon": "⭐", "text": "50守护者经验", "type": "guardian_exp", "amount": 50, "position": 0},
            {"id": 2, "icon": "⭐", "text": "20守护者经验", "type": "guardian_exp", "amount": 20, "position": 1},
            {"id": 3, "icon": "⭐", "text": "100守护者经验", "type": "guardian_exp", "amount": 100, "position": 2},
            {"id": 4, "icon": "⭐", "text": "30守护者经验", "type": "guardian_exp", "amount": 30, "position": 3},
            {"id": 5, "icon": "⭐", "text": "200守护者经验", "type": "guardian_exp", "amount": 200, "position": 4},
            {"id": 6, "icon": "⭐", "text": "300守护者经验", "type": "guardian_exp", "amount": 300, "position": 5},
            {"id": 7, "icon": "⭐", "text": "150守护者经验", "type": "guardian_exp", "amount": 150, "position": 6},
            {"id": 8, "icon": "⭐", "text": "40守护者经验", "type": "guardian_exp", "amount": 40, "position": 7},
        ]
    
    async def _ensure_redis_initialized(self):
        """确保Redis管理器已初始化"""
        if not redis_manager._initialized:
            await redis_manager.initialize()
    
    async def get_user_lottery_status(self, user_id: int) -> Dict[str, Any]:
        """获取用户转盘状态"""
        today = datetime.now().date()
        redis_key = f"lottery:user:{user_id}:date:{today}"
        
        try:
            # 确保Redis管理器已初始化
            await self._ensure_redis_initialized()
            
            # 从Redis获取今日抽奖状态
            status_data = await redis_manager.get(redis_key)
            if status_data:
                status = json.loads(status_data)
            else:
                # 初始化今日转盘状态
                status = {
                    "date": str(today),
                    "claimed_prize_ids": [],
                    "daily_count": 0,
                    "total_prizes": len(self.default_prizes)
                }
                await redis_manager.set(redis_key, json.dumps(status), expire=86400)  # 24小时过期
            
            # 构建奖品列表，标记已抽中的奖品
            prizes = []
            for prize in self.default_prizes:
                prize_copy = prize.copy()
                prize_copy["is_claimed"] = prize["id"] in status["claimed_prize_ids"]
                prize_copy["can_win"] = not prize_copy["is_claimed"]
                prizes.append(prize_copy)
            
            return {
                "prizes": prizes,
                "daily_count": status["daily_count"],
                "total_prizes": status["total_prizes"],
                "claimed_prize_ids": status["claimed_prize_ids"],
                "remaining_count": status["total_prizes"] - status["daily_count"]
            }
            
        except Exception as e:
            logger.error(f"获取转盘状态失败 user_id={user_id}: {e}")
            raise e
    
    async def spin_lottery(self, user_id: int) -> Dict[str, Any]:
        """执行转盘抽奖"""
        today = datetime.now().date()
        redis_key = f"lottery:user:{user_id}:date:{today}"
        
        try:
            # 确保Redis管理器已初始化
            await self._ensure_redis_initialized()
            
            # 获取当前状态
            status_data = await redis_manager.get(redis_key)
            if not status_data:
                # 初始化状态
                status = {
                    "date": str(today),
                    "claimed_prize_ids": [],
                    "daily_count": 0,
                    "total_prizes": len(self.default_prizes)
                }
            else:
                status = json.loads(status_data)
            
            # 检查是否还有奖品可抽
            available_prizes = [p for p in self.default_prizes if p["id"] not in status["claimed_prize_ids"]]
            if not available_prizes:
                return {
                    "success": False,
                    "message": "所有奖品已抽完，请等待明日重置",
                    "remaining_count": 0
                }
            
            # 随机选择一个可用奖品
            selected_prize = random.choice(available_prizes)
            
            # 发放奖励
            reward_result = await self._give_reward(user_id, selected_prize)
            if not reward_result["success"]:
                return reward_result
            
            # 更新抽奖状态
            status["claimed_prize_ids"].append(selected_prize["id"])
            status["daily_count"] += 1
            
            # 保存到Redis
            await redis_manager.set(redis_key, json.dumps(status), expire=86400)
            
            logger.info(f"用户 {user_id} 转盘抽中奖品: {selected_prize['text']}")
            
            return {
                "success": True,
                "prize": selected_prize,
                "remaining_count": len(self.default_prizes) - status["daily_count"],
                "message": f"恭喜获得 {selected_prize['text']}!"
            }
            
        except Exception as e:
            logger.error(f"转盘抽奖失败 user_id={user_id}: {e}")
            return {
                "success": False,
                "message": "抽奖失败，请稍后重试",
                "remaining_count": 0
            }
    
    async def claim_ad_double_reward(self, user_id: int, prize_id: int) -> Dict[str, Any]:
        """观看广告获得双倍奖励"""
        try:
            # 找到对应奖品
            prize = next((p for p in self.default_prizes if p["id"] == prize_id), None)
            if not prize:
                return {
                    "success": False,
                    "message": "奖品不存在"
                }
            
            # 发放双倍奖励（原始奖励数量）
            double_prize = prize.copy()
            reward_result = await self._give_reward(user_id, double_prize)
            
            if reward_result["success"]:
                logger.info(f"用户 {user_id} 观看广告获得双倍奖励: {prize['text']}")
                return {
                    "success": True,
                    "message": f"观看广告获得额外 {prize['text']}!",
                    "prize": double_prize
                }
            else:
                return reward_result
                
        except Exception as e:
            logger.error(f"广告双倍奖励失败 user_id={user_id}, prize_id={prize_id}: {e}")
            return {
                "success": False,
                "message": "获取双倍奖励失败，请稍后重试"
            }
    
    async def _give_reward(self, user_id: int, prize: Dict[str, Any]) -> Dict[str, Any]:
        """发放奖励"""
        try:
            if prize["type"] == "guardian_exp":
                # 发放守护经验值
                result = await self.guardian_service.gain_experience(
                    user_id=user_id,
                    experience_amount=prize["amount"],
                    source="lottery_wheel",
                    context={"description": f"转盘抽奖获得 {prize['amount']} 守护经验"}
                )
                if result["success"]:
                    return {
                        "success": True,
                        "message": f"获得 {prize['amount']} 守护经验值"
                    }
                else:
                    return result
                    
            elif prize["type"] == "stamina":
                # 发放体力值
                result = await self.stamina_service.add_stamina(
                    user_id=user_id,
                    amount=prize["amount"],
                    source="lottery_wheel",
                    description=f"转盘抽奖获得 {prize['amount']} 体力值"
                )
                if result["success"]:
                    return {
                        "success": True,
                        "message": f"获得 {prize['amount']} 体力值"
                    }
                else:
                    return result
                    
            elif prize["type"] == "cultural_atlas":
                # 发放文化图鉴（暂时返回成功，后续可扩展）
                logger.info(f"用户 {user_id} 获得文化图鉴奖励")
                return {
                    "success": True,
                    "message": "获得文化图鉴奖励"
                }
                
            else:
                return {
                    "success": False,
                    "message": f"未知奖励类型: {prize['type']}"
                }
                
        except Exception as e:
            logger.error(f"发放奖励失败 user_id={user_id}, prize={prize}: {e}")
            return {
                "success": False,
                "message": "发放奖励失败，请稍后重试"
            }
    
    async def reset_daily_lottery(self) -> Dict[str, Any]:
        """重置所有用户的每日转盘状态（定时任务调用）"""
        try:
            # 确保Redis管理器已初始化
            await self._ensure_redis_initialized()
            
            # 删除所有转盘相关的Redis键
            pattern = "lottery:user:*"
            keys = await redis_manager.keys(pattern)
            
            if keys:
                await redis_manager.delete(*keys)
                logger.info(f"已重置 {len(keys)} 个用户的转盘状态")
            
            return {
                "success": True,
                "message": f"成功重置 {len(keys)} 个用户的转盘状态"
            }
            
        except Exception as e:
            logger.error(f"重置转盘状态失败: {e}")
            return {
                "success": False,
                "message": "重置转盘状态失败"
            }
    
    async def get_lottery_statistics(self) -> Dict[str, Any]:
        """获取转盘统计信息"""
        try:
            # 确保Redis管理器已初始化
            await self._ensure_redis_initialized()
            
            # 获取所有转盘相关键
            pattern = "lottery:user:*"
            keys = await redis_manager.keys(pattern)
            
            total_users = len(keys)
            total_spins = 0
            prize_stats = {}
            
            for key in keys:
                try:
                    data = await redis_manager.get(key)
                    if data:
                        status = json.loads(data)
                        total_spins += status.get("daily_count", 0)
                        
                        # 统计各奖品被抽中次数
                        for prize_id in status.get("claimed_prize_ids", []):
                            prize_stats[prize_id] = prize_stats.get(prize_id, 0) + 1
                            
                except Exception as key_error:
                    logger.warning(f"处理键 {key} 时出错: {key_error}")
                    continue
            
            return {
                "success": True,
                "data": {
                    "total_users": total_users,
                    "total_spins": total_spins,
                    "prize_statistics": prize_stats,
                    "average_spins_per_user": total_spins / total_users if total_users > 0 else 0
                }
            }
            
        except Exception as e:
            logger.error(f"获取转盘统计失败: {e}")
            return {
                "success": False,
                "message": "获取统计信息失败"
            }