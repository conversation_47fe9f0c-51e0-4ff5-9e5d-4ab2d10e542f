"""
转盘抽奖API端点
提供转盘状态查询、抽奖、广告双倍奖励等功能
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any
from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.services.lottery_service import LotteryService
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/status")
async def get_lottery_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取用户转盘状态
    返回奖品列表、已抽中奖品、剩余次数等信息
    """
    try:
        lottery_service = LotteryService(db)
        status = await lottery_service.get_user_lottery_status(current_user.id)
        
        return {
            "success": True,
            "data": status,
            "message": "获取转盘状态成功"
        }
        
    except Exception as e:
        logger.error(f"获取转盘状态失败 user_id={current_user.id}: {e}")
        raise HTTPException(status_code=500, detail="获取转盘状态失败")

@router.post("/spin")
async def spin_lottery(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    执行转盘抽奖
    随机选择一个未抽中的奖品，发放对应奖励
    """
    try:
        lottery_service = LotteryService(db)
        result = await lottery_service.spin_lottery(current_user.id)
        
        if result["success"]:
            return {
                "success": True,
                "data": result,
                "message": result["message"]
            }
        else:
            return {
                "success": False,
                "data": result,
                "message": result["message"]
            }
            
    except Exception as e:
        logger.error(f"转盘抽奖失败 user_id={current_user.id}: {e}")
        raise HTTPException(status_code=500, detail="转盘抽奖失败")

@router.post("/ad-double-reward")
async def claim_ad_double_reward(
    prize_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    观看广告获得双倍奖励
    在抽中奖品后，可观看广告获得相同数量的额外奖励
    """
    try:
        lottery_service = LotteryService(db)
        result = await lottery_service.claim_ad_double_reward(current_user.id, prize_id)
        
        if result["success"]:
            return {
                "success": True,
                "data": result,
                "message": result["message"]
            }
        else:
            return {
                "success": False,
                "data": result,
                "message": result["message"]
            }
            
    except Exception as e:
        logger.error(f"广告双倍奖励失败 user_id={current_user.id}, prize_id={prize_id}: {e}")
        raise HTTPException(status_code=500, detail="获取双倍奖励失败")

@router.get("/statistics")
async def get_lottery_statistics(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取转盘统计信息（管理员或用户个人统计）
    """
    try:
        lottery_service = LotteryService(db)
        stats = await lottery_service.get_lottery_statistics()
        
        return {
            "success": True,
            "data": stats.get("data", {}),
            "message": "获取统计信息成功"
        }
        
    except Exception as e:
        logger.error(f"获取转盘统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取统计信息失败")

@router.post("/admin/reset-daily")
async def reset_daily_lottery(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员手动重置每日转盘状态
    通常由定时任务自动执行，此接口用于紧急重置
    """
    try:
        # 这里可以添加管理员权限检查
        # if not current_user.is_admin:
        #     raise HTTPException(status_code=403, detail="权限不足")
        
        lottery_service = LotteryService(db)
        result = await lottery_service.reset_daily_lottery()
        
        return {
            "success": True,
            "data": result,
            "message": result["message"]
        }
        
    except Exception as e:
        logger.error(f"重置转盘状态失败: {e}")
        raise HTTPException(status_code=500, detail="重置转盘状态失败")