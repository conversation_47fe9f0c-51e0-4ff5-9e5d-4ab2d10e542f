<template>
  <div v-if="show" class="monument-quiz-overlay">
    <div class="monument-quiz-container">
      <!-- 顶部横幅标题 -->
      <div class="title-banner">
        <div class="banner-ribbon">
          <h1 class="banner-title">{{ currentMonument.name }}</h1>
          <div class="experience-badge" v-if="!isRestored">+{{ successReward }}</div>
        </div>
      </div>

      <!-- 古迹卡片 -->
      <div class="monument-card">
        <div class="card-frame">
          <div class="card-inner">
            <div class="monument-image-container">
              <img
                :src="currentMonument.pollutedImage || '/images/monuments/default-polluted.jpg'"
                v-if="!isRestored"
                alt="被污染的古迹"
                class="monument-image polluted"
              />
              <img
                :src="currentMonument.cleanImage || '/images/monuments/default-clean.jpg'"
                v-else
                alt="恢复的古迹"
                class="monument-image clean"
              />
              <div v-if="!isRestored" class="pollution-overlay">
                <div class="pollution-effect"></div>
              </div>
            </div>
            <div class="card-title">{{ currentMonument.name }}</div>
          </div>
        </div>
      </div>

      <!-- 国王角色对话 -->
      <div class="king-dialogue" v-if="!isRestored && !showResult">
        <div class="king-avatar">
          <div class="crown">👑</div>
          <div class="king-face">🤴</div>
        </div>
        <div class="dialogue-bubble">
          <p class="king-text">
            嘿！别以为抢回去就是你的了！这件宝贝的故事你懂多少？敢不敢回答我的问题，证明你不是个草包？
          </p>
        </div>
      </div>

      <!-- 成功恢复对话 -->
      <div class="king-dialogue success" v-else-if="isRestored">
        <div class="king-avatar">
          <div class="crown">👑</div>
          <div class="king-face">😤</div>
        </div>
        <div class="dialogue-bubble success">
          <p class="king-text">
            算你厉害，我们先撤，但我还会回来的！！！
          </p>
        </div>
      </div>

      <!-- 问答区域 -->
      <div class="quiz-section" v-if="!isRestored && !showResult">
        <div class="question-container">
          <div class="question-card">
            <div class="question-header">
              <div class="question-progress">{{ currentQuestionIndex + 1 }}/{{ currentMonument.questions.length }}</div>
            </div>

            <div class="question-content">
              <h3 class="question-text">{{ currentQuestion.question }}</h3>

              <div class="options-container">
                <button
                  v-for="(option, index) in currentQuestion.options"
                  :key="index"
                  class="option-btn"
                  :class="{
                    'selected': selectedAnswer === index,
                    'correct': showAnswerResult && index === currentQuestion.correctAnswer,
                    'wrong': showAnswerResult && selectedAnswer === index && index !== currentQuestion.correctAnswer
                  }"
                  @click="selectAnswer(index)"
                  :disabled="showAnswerResult"
                >
                  <span class="option-label">{{ String.fromCharCode(65 + index) }}</span>
                  <span class="option-text">{{ option }}</span>
                </button>
              </div>

              <div class="explanation" v-if="showAnswerResult && currentQuestion.explanation">
                <p><strong>解析：</strong>{{ currentQuestion.explanation }}</p>
              </div>
            </div>
          </div>

          <div class="quiz-actions">
            <button
              v-if="!showAnswerResult && selectedAnswer !== null"
              class="challenge-btn"
              @click="submitAnswer"
            >
              挑战
            </button>
            <button
              v-if="showAnswerResult"
              class="next-btn"
              @click="nextQuestion"
            >
              {{ currentQuestionIndex < currentMonument.questions.length - 1 ? '下一题' : '完成' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 结果展示 -->
      <div class="result-section" v-if="showResult">
        <div class="result-container" :class="{ 'success': isSuccess, 'failure': !isSuccess }">
          <div class="result-icon">
            <span v-if="isSuccess">🎉</span>
            <span v-else>😞</span>
          </div>
          
          <h3 class="result-title">
            {{ isSuccess ? '恭喜！古迹已恢复！' : '很遗憾，保护失败...' }}
          </h3>
          
          <div class="result-stats">
            <p>正确答案: {{ correctAnswers }} / {{ currentMonument.questions.length }}</p>
            <p class="reward-text" v-if="isSuccess">
              获得 {{ successReward }} 文明经验值！
            </p>
            <p class="penalty-text" v-else>
              失去 {{ failurePenalty }} 文明经验值...
            </p>
          </div>
          
          <div class="result-actions">
            <button 
              v-if="isSuccess" 
              class="reward-btn normal-reward"
              @click="claimReward(false)"
            >
              获得奖励
            </button>
            <button 
              v-if="isSuccess" 
              class="reward-btn double-reward"
              @click="claimReward(true)"
            >
              看广告获得双倍
            </button>
            <button 
              v-else
              class="retry-btn"
              @click="retryQuiz"
            >
              重新挑战
            </button>
            <button 
              class="close-btn"
              @click="closeQuiz"
            >
              {{ isSuccess ? '继续游戏' : '离开' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useGameStore } from '@/stores/game'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  monumentId: {
    type: String,
    default: 'monument_1'
  }
})

const emit = defineEmits(['close', 'success', 'failure'])

const gameStore = useGameStore()

// 古迹数据
const monuments = ref({
  monument_1: {
    name: "拉奥孔与他的儿子",
    pollutedImage: "/img/map_Vatican.ae8dbd6c.png",
    cleanImage: "/img/map_Vatican.ae8dbd6c.png",
    questions: [
      {
        question: "拉奥孔这尊雕塑伊战争中扮演什么角色？你得知道他是谁！测试，测试，测试。",
        options: ["希腊的将军", "特洛伊的国王", "特洛伊的祭司", "木马的设计者"],
        correctAnswer: 2,
        explanation: "拉奥孔是特洛伊的祭司，传说海神波塞冬或阿波罗"
      },
      {
        question: "这座雕塑现在收藏在哪里？",
        options: ["卢浮宫", "大英博物馆", "梵蒂冈博物馆", "乌菲兹美术馆"],
        correctAnswer: 2,
        explanation: "拉奥孔群雕现收藏在梵蒂冈博物馆"
      },
      {
        question: "拉奥孔群雕是什么时期的作品？",
        options: ["古希腊时期", "古罗马时期", "文艺复兴时期", "巴洛克时期"],
        correctAnswer: 1,
        explanation: "这是古罗马时期的大理石雕塑作品"
      },
      {
        question: "雕塑中拉奥孔和他的儿子们被什么攻击？",
        options: ["狮子", "海蛇", "老鹰", "巨龙"],
        correctAnswer: 1,
        explanation: "根据神话，拉奥孔和他的儿子们被海蛇缠绕攻击"
      },
      {
        question: "这座雕塑体现了什么艺术风格？",
        options: ["古典主义", "浪漫主义", "现实主义", "希腊化风格"],
        correctAnswer: 3,
        explanation: "拉奥孔群雕体现了希腊化时期的艺术风格，注重情感表达"
      }
    ]
  },
  monument_2: {
    name: "紫禁城",
    pollutedImage: "/img/map_Shanghai.624b3b9d.png",
    cleanImage: "/img/map_Shanghai.624b3b9d.png",
    questions: [
      {
        question: "紫禁城建于哪个朝代？",
        options: ["唐朝", "宋朝", "明朝", "清朝"],
        correctAnswer: 2,
        explanation: "紫禁城始建于明朝永乐年间"
      },
      {
        question: "紫禁城有多少间房屋？",
        options: ["6666间", "7777间", "8888间", "9999间"],
        correctAnswer: 3,
        explanation: "传说紫禁城有9999间半房屋"
      },
      {
        question: "紫禁城的正门叫什么？",
        options: ["天安门", "午门", "神武门", "太和门"],
        correctAnswer: 1,
        explanation: "午门是紫禁城的正门，也是皇宫的南门"
      }
    ]
  }
})

// 游戏状态
const currentQuestionIndex = ref(0)
const selectedAnswer = ref(null)
const showAnswerResult = ref(false)
const showResult = ref(false)
const isRestored = ref(false)
const correctAnswers = ref(0)
const successReward = ref(15)
const failurePenalty = ref(10)

// 计算属性
const currentMonument = computed(() => {
  return monuments.value[props.monumentId] || monuments.value.monument_1
})

const currentQuestion = computed(() => {
  return currentMonument.value.questions[currentQuestionIndex.value]
})

const isSuccess = computed(() => {
  return correctAnswers.value >= Math.ceil(currentMonument.value.questions.length * 0.6) // 60%正确率算成功
})

// 监听显示状态
watch(() => props.show, (newShow) => {
  if (newShow) {
    resetQuiz()
  }
})

// 重置问答
const resetQuiz = () => {
  currentQuestionIndex.value = 0
  selectedAnswer.value = null
  showAnswerResult.value = false
  showResult.value = false
  isRestored.value = false
  correctAnswers.value = 0
}

// 选择答案
const selectAnswer = (index) => {
  if (showAnswerResult.value) return
  selectedAnswer.value = index
}

// 提交答案
const submitAnswer = () => {
  showAnswerResult.value = true
  
  if (selectedAnswer.value === currentQuestion.value.correctAnswer) {
    correctAnswers.value++
  }
  
  // 2秒后允许进入下一题
  setTimeout(() => {
    if (currentQuestionIndex.value < currentMonument.value.questions.length - 1) {
      // 还有题目，等待用户点击下一题
    } else {
      // 所有题目完成，显示结果
      finishQuiz()
    }
  }, 1500)
}

// 下一题
const nextQuestion = () => {
  if (currentQuestionIndex.value < currentMonument.value.questions.length - 1) {
    currentQuestionIndex.value++
    selectedAnswer.value = null
    showAnswerResult.value = false
  } else {
    finishQuiz()
  }
}

// 完成问答
const finishQuiz = () => {
  showResult.value = true
  
  if (isSuccess.value) {
    isRestored.value = true
    gameStore.protectMonument(true)
    emit('success', {
      monumentId: props.monumentId,
      score: correctAnswers.value,
      total: currentMonument.value.questions.length
    })
  } else {
    gameStore.protectMonument(false)
    emit('failure', {
      monumentId: props.monumentId,
      score: correctAnswers.value,
      total: currentMonument.value.questions.length
    })
  }
}

// 领取奖励
const claimReward = (isDouble) => {
  const finalReward = isDouble ? successReward.value * 2 : successReward.value
  
  if (isDouble) {
    playRewardAd(() => {
      gameStore.addCivilizationExp(finalReward)
      closeQuiz()
    })
  } else {
    // 已经在protectMonument中给过奖励了，这里不重复给
    closeQuiz()
  }
}

// 重新挑战
const retryQuiz = () => {
  resetQuiz()
}

// 关闭问答
const closeQuiz = () => {
  emit('close')
}

// 播放奖励广告
const playRewardAd = (callback) => {
  if (window.CrazyGames && window.CrazyGames.SDK) {
    const adCallbacks = {
      adFinished: () => {
        console.log("广告播放完成")
        callback()
      },
      adError: (error) => {
        console.log("广告播放失败", error)
        callback()
      },
      adStarted: () => console.log("开始播放广告")
    }
    window.CrazyGames.SDK.ad.requestAd("rewarded", adCallbacks)
  } else {
    setTimeout(callback, 1000)
  }
}
</script>

<style lang="scss" scoped>
.monument-quiz-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, #ff69b4 0%, #87ceeb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
  animation: fadeIn 0.5s ease-in-out;
}

.monument-quiz-container {
  background: transparent;
  border-radius: 20px;
  padding: 20px;
  max-width: 400px;
  max-height: 90vh;
  overflow-y: auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

// 顶部横幅标题
.title-banner {
  width: 100%;
  margin-bottom: 20px;

  .banner-ribbon {
    background: linear-gradient(135deg, #ffd700, #ffb347);
    border: 3px solid #8b4513;
    border-radius: 25px;
    padding: 15px 30px;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);

    &:before, &:after {
      content: '';
      position: absolute;
      top: 50%;
      width: 20px;
      height: 20px;
      background: #8b4513;
      border-radius: 50%;
      transform: translateY(-50%);
    }

    &:before {
      left: -10px;
    }

    &:after {
      right: -10px;
    }

    .banner-title {
      color: #8b4513;
      font-size: 20px;
      font-weight: bold;
      margin: 0;
      text-align: center;
      text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.5);
    }

    .experience-badge {
      position: absolute;
      top: -10px;
      right: 20px;
      background: #4CAF50;
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: bold;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
  }
}

// 古迹卡片
.monument-card {
  width: 100%;
  margin-bottom: 20px;

  .card-frame {
    background: linear-gradient(145deg, #f0f0f0, #d0d0d0);
    border: 4px solid #8b4513;
    border-radius: 20px;
    padding: 8px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    transform: perspective(1000px) rotateX(5deg);

    .card-inner {
      background: linear-gradient(145deg, #fff, #f5f5f5);
      border: 2px solid #ddd;
      border-radius: 15px;
      padding: 15px;
      text-align: center;

      .monument-image-container {
        position: relative;
        margin-bottom: 10px;

        .monument-image {
          width: 100%;
          max-width: 280px;
          height: 200px;
          object-fit: cover;
          border-radius: 10px;
          border: 2px solid #8b4513;

          &.polluted {
            filter: hue-rotate(120deg) saturate(0.5) brightness(0.7);
          }

          &.clean {
            animation: restore 1s ease-in-out;
          }
        }

        .pollution-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border-radius: 10px;
          pointer-events: none;

          .pollution-effect {
            width: 100%;
            height: 100%;
            background: rgba(139, 69, 19, 0.3);
            animation: pollutionPulse 2s infinite;
          }
        }
      }

      .card-title {
        font-size: 16px;
        color: #8b4513;
        font-weight: bold;
        margin: 0;
      }
    }
  }
}

// 国王对话
.king-dialogue {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 20px;
  width: 100%;

  .king-avatar {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-shrink: 0;

    .crown {
      font-size: 24px;
      margin-bottom: -5px;
      animation: bounce 2s infinite;
    }

    .king-face {
      font-size: 48px;
      filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
    }
  }

  .dialogue-bubble {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 15px;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border: 2px solid #ddd;

    &:before {
      content: '';
      position: absolute;
      left: -10px;
      top: 20px;
      border: 10px solid transparent;
      border-right-color: #ddd;
    }

    &:after {
      content: '';
      position: absolute;
      left: -8px;
      top: 22px;
      border: 8px solid transparent;
      border-right-color: rgba(255, 255, 255, 0.95);
    }

    &.success {
      background: rgba(76, 175, 80, 0.1);
      border-color: #4CAF50;

      &:before {
        border-right-color: #4CAF50;
      }

      &:after {
        border-right-color: rgba(76, 175, 80, 0.1);
      }
    }

    .king-text {
      color: #333;
      font-size: 14px;
      margin: 0;
      line-height: 1.4;
      font-weight: 500;
    }
  }
}

// 问答区域
.quiz-section {
  width: 100%;
  margin-bottom: 20px;

  .question-container {
    .question-card {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 15px;
      padding: 20px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      margin-bottom: 15px;

      .question-header {
        text-align: center;
        margin-bottom: 15px;

        .question-progress {
          background: #666;
          color: white;
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 12px;
          display: inline-block;
        }
      }

      .question-content {
        .question-text {
          font-size: 16px;
          color: #333;
          margin-bottom: 20px;
          text-align: left;
          line-height: 1.5;
          font-weight: 500;
        }

        .options-container {
          display: flex;
          flex-direction: column;
          gap: 10px;
          margin-bottom: 15px;

          .option-btn {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            background: #f5f5f5;
            border: 2px solid #ddd;
            border-radius: 8px;
            color: #333;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;

            .option-label {
              background: #666;
              color: white;
              width: 24px;
              height: 24px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              font-weight: bold;
              margin-right: 12px;
              flex-shrink: 0;
            }

            .option-text {
              flex: 1;
            }

            &:hover:not(:disabled) {
              background: #e8f4fd;
              border-color: #2196F3;
              transform: translateY(-1px);
            }

            &.selected {
              border-color: #ff5722;
              background: #fff3e0;

              .option-label {
                background: #ff5722;
              }
            }

            &.correct {
              border-color: #4CAF50;
              background: #e8f5e8;

              .option-label {
                background: #4CAF50;
              }
            }

            &.wrong {
              border-color: #f44336;
              background: #ffebee;

              .option-label {
                background: #f44336;
              }
            }

            &:disabled {
              cursor: not-allowed;
            }
          }
        }

        .explanation {
          background: #f0f8ff;
          border: 1px solid #b3d9ff;
          border-radius: 8px;
          padding: 12px;
          margin-top: 15px;

          p {
            margin: 0;
            font-size: 13px;
            color: #333;
            line-height: 1.4;
          }
        }
      }
    }

    .quiz-actions {
      display: flex;
      justify-content: center;

      .challenge-btn, .next-btn {
        padding: 12px 40px;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      .challenge-btn {
        background: linear-gradient(135deg, #ff9800, #f57c00);
        color: white;

        &:hover {
          background: linear-gradient(135deg, #f57c00, #ef6c00);
          transform: translateY(-2px);
          box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        }
      }

      .next-btn {
        background: linear-gradient(135deg, #ff9800, #f57c00);
        color: white;

        &:hover {
          background: linear-gradient(135deg, #f57c00, #ef6c00);
          transform: translateY(-2px);
          box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }
}

.result-section {
  text-align: center;
  
  .result-container {
    padding: 30px;
    border-radius: 20px;
    
    &.success {
      background: linear-gradient(135deg, #1a4a1a, #2a6a2a);
      border: 3px solid #4CAF50;
    }
    
    &.failure {
      background: linear-gradient(135deg, #4a1a1a, #6a2a2a);
      border: 3px solid #f44336;
    }
    
    .result-icon {
      font-size: 60px;
      margin-bottom: 20px;
    }
    
    .result-title {
      font-size: 24px;
      color: #fff;
      margin-bottom: 20px;
    }
    
    .result-stats {
      margin-bottom: 30px;
      
      p {
        color: #ccc;
        font-size: 16px;
        margin: 10px 0;
      }
      
      .reward-text {
        color: #4CAF50;
        font-weight: bold;
      }
      
      .penalty-text {
        color: #f44336;
        font-weight: bold;
      }
    }
    
    .result-actions {
      display: flex;
      gap: 15px;
      justify-content: center;
      flex-wrap: wrap;
      
      .reward-btn, .retry-btn, .close-btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
      }
      
      .normal-reward {
        background: linear-gradient(135deg, #4CAF50, #45a049);
        color: white;
      }
      
      .double-reward {
        background: linear-gradient(135deg, #ff9800, #f57c00);
        color: white;
        
        &:after {
          content: "🎬";
          margin-left: 8px;
        }
      }
      
      .retry-btn {
        background: linear-gradient(135deg, #ff9800, #f57c00);
        color: white;
      }
      
      .close-btn {
        background: linear-gradient(135deg, #607D8B, #455A64);
        color: white;
      }
    }
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes restore {
  from {
    filter: hue-rotate(120deg) saturate(0.5) brightness(0.7);
    transform: scale(0.9);
  }
  to {
    filter: none;
    transform: scale(1);
  }
}

@keyframes pollutionPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@media (max-width: 768px) {
  .monument-quiz-container {
    padding: 15px;
    margin: 10px;
    max-width: 350px;
  }

  .title-banner {
    .banner-ribbon {
      padding: 12px 20px;

      .banner-title {
        font-size: 18px;
      }
    }
  }

  .monument-card {
    .card-frame {
      .card-inner {
        padding: 12px;

        .monument-image-container {
          .monument-image {
            height: 160px;
          }
        }

        .card-title {
          font-size: 14px;
        }
      }
    }
  }

  .king-dialogue {
    .king-avatar {
      .crown {
        font-size: 20px;
      }

      .king-face {
        font-size: 40px;
      }
    }

    .dialogue-bubble {
      .king-text {
        font-size: 13px;
      }
    }
  }

  .quiz-section {
    .question-container {
      .question-card {
        padding: 15px;

        .question-content {
          .question-text {
            font-size: 15px;
          }

          .options-container {
            .option-btn {
              padding: 10px 12px;
              font-size: 13px;

              .option-label {
                width: 20px;
                height: 20px;
                font-size: 11px;
                margin-right: 10px;
              }
            }
          }
        }
      }

      .quiz-actions {
        .challenge-btn, .next-btn {
          padding: 10px 30px;
          font-size: 14px;
        }
      }
    }
  }

  .result-actions {
    flex-direction: column;

    .reward-btn, .retry-btn, .close-btn {
      width: 100%;
    }
  }
}
</style>