<template>
  <div>

    <van-popup
      v-model:show="visible"
      position="center"
      :style="{ overflow: 'visible' }"
      closeable
      :close-icon="'/img/page_icons/close-btn.png'"
      close-icon-position="top-right"
      :overlay-style="{
        'background': 'rgba(0, 0, 0, 0.8)!important',
        'backdrop-filter': 'blur(10px)!important',
      }"
      round
    >
      <div class="lottery-wheel-container">
        <div class="lottery-title">
          {{ t('lottery.title') }}
        </div>
        <div class="lottery-notice">
          {{ t('lottery.title') }}
          <!-- {{ t('lottery.notice') }}
          <span class="label">{{ t('lottery.remaining_times') }}</span>
          <span class="value">{{ actualPrizes.filter(p => p.can_win).length }}</span> -->
        </div>
        <!-- 转盘主体 -->
        <div class="wheel-wrapper">
          <!-- 转盘底座背景 -->
          <div class="wheel-base">
            <img 
              src="/img/page_icons/spin_base.png" 
              alt="转盘底座" 
              @error="handleBaseImageError"
            />
          </div>
  
          <!-- 旋转转盘 -->
          <div 
            class="wheel-rotate" 
            :class="{ spinning: isSpinning }" 
            :style="{ transform: `rotate(${wheelRotation}deg)` }"
          >
            <!-- 转盘背景图 -->
            <img 
              class="wheel-bg"
              src="/img/page_icons/spin_wheel.png" 
              alt="转盘" 
              @error="handleWheelImageError"
            />
            
            <!-- 奖品覆盖层 -->
            <div class="prizes-container">
              <div
                v-for="(prize, index) in prizes"
                :key="prize.id || index"
                class="prize-item"
                :style="getPrizeStyle(index)"
              >
                <!-- 内圈奖品数量 -->
                <div class="prize-amount-wrapper" :style="{ transform: `translateX(-50%) rotate(-${(360 / prizes.length) * index + (360 / prizes.length) / 2}deg)` }">
                  <div class="prize-amount" :class="{ 'claimed': prize.is_claimed }">
                    <span class="amount-text" :style="{ transform: `rotate(${(360 / prizes.length) * index + (360 / prizes.length) / 2}deg)` }">
                      {{ prize.is_claimed ? '已抽完' : (prize.amount || prize.text.match(/\d+/)?.[0] || '') }}
                    </span>
                  </div>
                </div>
                <!-- 外圈奖品图片 -->
                <div class="prize-icon-wrapper" :style="{ transform: `translateX(-50%) rotate(-${(360 / prizes.length) * index + (360 / prizes.length) / 2}deg)` }">
                  <div class="prize-icon" :class="{ 'claimed': prize.is_claimed, 'civilization-exp': prize.type === 'civilization_exp', 'stamina': prize.type === 'stamina', 'cultural-atlas': prize.type === 'cultural_atlas' }">
                    <img v-if="prize.type === 'stamina'" src="" alt="体力值" />
                    <img v-if="prize.type === 'guardian_exp'" src="/img/page_icons/guardian_exp2.png" alt="守护者经验值" />
                    <img v-if="prize.type === 'cultural_atlas'" src="" alt="文化图鉴" />
                  </div>
                </div>
              </div>
            </div>
            
  
          </div>
  
  
          <!-- 顶部指针 -->
          <div class="pointer">
            <img src="/img/page_icons/spin_pointer.png" alt="指针" />
          </div>
        </div>
  
        <!-- 抽奖信息 -->
        <div class="lottery-info" @click="spinWheel" :class="{ disabled: isSpinning || actualPrizes.filter(p => p.can_win).length <= 0 }">
          {{ isSpinning ? t('lottery.start_ing') : (actualPrizes.filter(p => p.can_win).length > 0 ? t('lottery.start') : '已抽完') }}
        </div>
  
      </div>
    </van-popup>
    <!-- 中奖提示 -->
    <!-- <transition name="prize-fade"> -->
      <div v-if="showPrizeResult" class="prize-result">
        <div class="result-content">
          <div class="result-prize">{{ lastPrize?.amount }}</div>
          <img class="result-icon" src="/img/page_icons/guardian_exp3.png" alt="奖励" />
          
          <!-- 双倍奖励按钮 -->
          <div v-if="!hasClaimedDouble" class="double-reward-btn" @click="claimDoubleReward">
            <img src="/img/page_icons/get_double_reward.png" alt="看广告双倍奖励" />
            <span>看广告双倍奖励</span>
          </div>
        </div>
      </div>
    <!-- </transition> -->
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { showFailToast } from 'vant'
import { api } from '@/utils/api'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:show', 'spin-complete', 'prize-won'])

// 响应式数据
const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const isSpinning = ref(false)
const wheelRotation = ref(0)
const lastPrize = ref(null)
const showPrizeResult = ref(false)
const actualPrizes = ref([])
const dailyCount = ref(0)
const dailyLimit = ref(0)
const claimedPrizeIds = ref([])
const hasClaimedDouble = ref(false)

// 计算属性
const remainingSpins = computed(() => dailyLimit.value - dailyCount.value)

// 转盘奖品配置 - 严格按照12点方向开始顺时针排列，使用守护者经验值系统
const defaultPrizes = [
        { id: 1, icon: '⭐', text: '50守护者经验', type: 'guardian_exp', amount: 50, position: 0 },     // 12点位置 (0度)
      { id: 2, icon: '', text: '20体力值', type: 'stamina', amount: 20, position: 1 },    // 顺时针第1个位置
      { id: 3, icon: '⭐', text: '100守护者经验', type: 'guardian_exp', amount: 100, position: 2 },     // 顺时针第2个位置
      { id: 4, icon: '', text: '30体力值', type: 'stamina', amount: 30, position: 3 },  // 顺时针第3个位置
      { id: 5, icon: '⭐', text: '200守护者经验', type: 'guardian_exp', amount: 200, position: 4 },     // 顺时针第4个位置
      { id: 6, icon: '📜', text: '文化图鉴', type: 'cultural_atlas', amount: 1, position: 5 },  // 顺时针第5个位置
      { id: 7, icon: '⭐', text: '150守护者经验', type: 'guardian_exp', amount: 150, position: 6 },    // 顺时针第6个位置
      { id: 8, icon: '', text: '40体力值', type: 'stamina', amount: 40, position: 7 },  // 顺时针第7个位置
]

// 创建ID到位置的映射表
const prizePositionMap = new Map()
defaultPrizes.forEach(prize => {
  prizePositionMap.set(prize.id, prize.position)
})

const prizes = computed(() => {
  // 确保奖品按照位置顺序排列（从12点开始顺时针）
  if (actualPrizes.value.length > 0) {
    // 为后端奖品数据添加位置信息
    const prizesWithPosition = actualPrizes.value.map(prize => ({
      ...prize,
      position: prizePositionMap.get(prize.id) ?? 0
    }))
    // 按位置排序，确保从12点开始顺时针排列
    return prizesWithPosition.sort((a, b) => a.position - b.position)
  }
  return defaultPrizes
})

// 计算每个奖品的位置样式 - 严格按照12点方向开始顺时针排列
const getPrizeStyle = (index) => {
  const total = prizes.value.length
  const segmentAngle = 360 / total  // 每个扇形的角度 (360/7 ≈ 51.43度)

  // 奖品放置在每个扇形的中心位置
  // index=0 的奖品在12点方向 (0度 + segmentAngle/2)
  // index=1 的奖品在第一个扇形中心 (segmentAngle + segmentAngle/2)
  const rotateAngle = index * segmentAngle 

  return {
    transform: `rotate(${rotateAngle}deg)`,
    transformOrigin: 'center bottom'
  }
}

// 加载奖品列表
const loadPrizes = async () => {
  try {
    const response = await api.lottery.getStatus()
    if (response.data.data) {
      // 使用可用奖品更新转盘
      actualPrizes.value = response.data.data.prizes || []
      dailyLimit.value = response.data.data.total_prizes || response.data.data.daily_limit || 8
      dailyCount.value = response.data.data.daily_count || 0
      claimedPrizeIds.value = response.data.data.claimed_prize_ids || []
      
      console.log('🎯 转盘状态更新:', {
        总奖品: actualPrizes.value.length,
        可用奖品: actualPrizes.value.filter(p => p.can_win).length,
        已抽取: dailyCount.value,
        奖品状态: actualPrizes.value.map(p => ({
          id: p.id,
          text: p.text,
          已抽取: p.is_claimed,
          可抽取: p.can_win
        }))
      })
    }
  } catch (error) {
    console.error('❌ 获取奖品列表失败:', error)
  }
}

// 转盘抽奖
const spinWheel = async () => {
  const availablePrizes = prizes.value.filter(p => p.can_win)
  if (isSpinning.value || availablePrizes.length <= 0) {
    if (availablePrizes.length <= 0) {
      showFailToast('所有奖品已抽完，请等待24小时重置')
    }
    return
  }

  isSpinning.value = true
  showPrizeResult.value = false

  try {
    const response = await api.lottery.spin()
    
    console.log('🎯 抽奖API响应:', response.data)
    
    if (response.data && response.data.success && response.data.data) {
      const selectedPrize = response.data.data.prize
      console.log('🎁 选中奖品:', selectedPrize)
      
      if (!selectedPrize) {
        console.error('❌ API返回的奖品数据为空')
        showFailToast('抽奖数据异常，请重试')
        isSpinning.value = false
        return
      }

      
      // 通过 ID 精确匹配奖品，获取其在转盘上的位置
      const targetPrize = prizes.value.find(p => p.id === selectedPrize.id)

      if (!targetPrize) {
        console.error('未找到对应奖品 ID:', selectedPrize.id, '奖品列表:', prizes.value)
        showFailToast('奖品数据异常，请重试')
        isSpinning.value = false
        return
      }

      // 获取奖品在转盘上的位置索引（从12点开始顺时针的第几个位置）
      const prizePosition = targetPrize.position ?? prizes.value.findIndex(p => p.id === selectedPrize.id)

      console.log('🎯 奖品位置:', prizePosition, '目标奖品:', targetPrize)

      // 计算转盘旋转角度
      const totalPrizes = prizes.value.length
      const segmentAngle = 360 / totalPrizes  // 每个扇形的角度

      // 计算目标奖品在转盘上的角度位置（奖品中心的角度）
      const prizeAngle = prizePosition * segmentAngle

      // 计算需要旋转的角度，让目标奖品的中心对准12点方向的指针
      // 指针在12点位置（0度），所以需要让奖品中心旋转到0度位置
      // 由于转盘是顺时针旋转，需要计算逆向角度

      // 获取当前转盘的实际角度位置（去除多圈旋转的影响）
      const currentAngle = wheelRotation.value % 360

      // 计算从当前位置到目标位置需要旋转的角度
      // 目标是让奖品中心对准12点方向（0度）
      let rotationNeeded = (360 - prizeAngle - currentAngle) % 360

      // 确保旋转角度为正值
      if (rotationNeeded < 0) {
        rotationNeeded += 360
      }

      // 添加多圈旋转增加视觉效果（5-7圈）
      const extraSpins = 5 + Math.floor(Math.random() * 3)
      const extraRotation = extraSpins * 360

      // 计算最终旋转角度（基于当前角度 + 额外旋转 + 精确角度）
      const finalRotation = wheelRotation.value + extraRotation + rotationNeeded
      
      wheelRotation.value = finalRotation
      // 等待动画完成
      setTimeout(() => {
        isSpinning.value = false
        lastPrize.value = selectedPrize
        showPrizeResult.value = true
        hasClaimedDouble.value = false // 重置双倍奖励状态
        
        
        emit('prize-won', selectedPrize)
        emit('spin-complete', {
          prize: selectedPrize,
          remainingCount: response.data.data.remaining_count || 0
        })

        // 重新加载奖品状态，更新转盘显示
        loadPrizes()

        // 自动隐藏结果（延长时间以便观看广告）
        setTimeout(() => {
          showPrizeResult.value = false
        }, 8000)
      }, 4000)
    } else {
      isSpinning.value = false
      showFailToast(response.data?.message || '抽奖失败，请重试')
    }
  } catch (error) {
    isSpinning.value = false
    console.error('抽奖失败:', error)
    showFailToast('网络错误，请稍后重试')
  }
}

// 领取双倍奖励
const claimDoubleReward = async () => {
  if (!lastPrize.value || hasClaimedDouble.value) {
    return
  }

  try {
    const response = await api.lottery.adDoubleReward(lastPrize.value.id)
    
    if (response.data && response.data.success) {
      hasClaimedDouble.value = true
      showFailToast(`观看广告获得额外 ${lastPrize.value.text}！`)
      
      // 发出双倍奖励事件
      emit('prize-won', {
        ...lastPrize.value,
        isDouble: true
      })
    } else {
      showFailToast(response.data?.message || '获取双倍奖励失败')
    }
  } catch (error) {
    console.error('获取双倍奖励失败:', error)
    showFailToast('网络错误，请稍后重试')
  }
}

// 图片错误处理
const handleBaseImageError = (e) => {
  e.target.style.display = 'none'
}

const handleWheelImageError = (e) => {
  console.warn('转盘图片加载失败')
  e.target.style.opacity = '0'
}

const handleButtonImageError = (e) => {
  e.target.style.display = 'none'
}

// 监听显示状态
watch(visible, (newVal) => {
  if (newVal) {
    showPrizeResult.value = false
    hasClaimedDouble.value = false
    loadPrizes()
  }
})

// 组件挂载
onMounted(() => {
  // loadPrizes()
})
</script>

<style lang="scss" scoped>
.lottery-wheel-container {
  width: 706px;
  height: 1266px;
  padding: 30px 20px;
  background: url('/img/page_icons/spin_content.png') no-repeat center center;
  background-size: cover;
  position: relative;
}

// 关闭按钮样式
:deep(.van-popup__close-icon--top-right) {
  top: -10px !important;
  right: -10px !important;
  .van-icon__image {
    width: 50px;
    height: 50px;
  }
}

.lottery-title {
  margin: 0 auto;
  margin-top: -60px;
  font-size: 30px;
  font-weight: bold;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  z-index: 10;
  width:586px;
  height: 104px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url('/img/page_icons/spin_title_bg.png') no-repeat center center;
  background-size: cover;
}

.lottery-notice {
  margin: 0 auto;
  margin-top: 20px;
  font-size: 28px;
  color: #fff;
  width:500px;
  height: 283px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding-bottom: 58px;
  background: url('/img/page_icons/spin_top.png') no-repeat center center;
  background-size: cover;
}
.wheel-wrapper {
  position: relative;
  width: 652px;
  height: 564px;
  margin: 0 auto 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

// 底座
.wheel-base {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

// 旋转转盘
.wheel-rotate {
  width: 430px;
  height: 430px;
  z-index: 2;
  transition: transform 4s cubic-bezier(0.17, 0.67, 0.12, 0.99);
  margin-top:36px;
  &.spinning {
    transition: transform 4s cubic-bezier(0.17, 0.67, 0.12, 0.99);
  }
  
  .wheel-bg {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}

// 奖品容器
.prizes-container {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}

// 奖品项
.prize-item {
  position: absolute;
  width: 100%;
  height: 50%;
  top: 0;
  left: 0;
  transform-origin: center bottom;
  
  // 内圈奖品数量
  .prize-amount-wrapper {
    position: absolute;
    top: 45%;  // 靠近内圈深色小圈
    left: 50%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .prize-amount {
      font-size: 24px;
      font-weight: bold;
      color: #fff;
      padding: 2px 6px;
      min-width: 20px;
      line-height: 1.2;
      
      &.claimed {
        font-size: 20px;
        color: #ccc;
      }
      
      .amount-text {
        display: inline-block;
      }
    }
  }
  
  // 外圈奖品图片
  .prize-icon-wrapper {
    position: absolute;
    top: 0%;   // 靠近外圈边缘
    left: 50%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .prize-icon {
      filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.8));
      img{
        width: 72px;
      }
      &.claimed {
        opacity: 0.4;
        filter: grayscale(100%) drop-shadow(0 0 2px rgba(128, 128, 128, 0.6));
      }
    }
  }
}


// 指针
.pointer {
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 5;
  img{
    width: 652px;
    height: 564px;
  }
  
}

// 抽奖
.lottery-info {
  margin: 0 auto;
  margin-top: 80px;
  font-size: 36px;
  font-weight: bold;
  color: #fff;
  text-align: center;
  width:208px;
  height: 96px;
  display: flex;
  // align-items: center;
  justify-content: flex-end;
  padding-right: 30px;
  padding-top: 10px;
  // padding-left: 70px;
  background: url('/img/page_icons/play_button.png') no-repeat center center;
  background-size: cover;
  cursor: pointer;
  // transition: transform 0.2s;
  
  &:active:not(.disabled) {
    transform: scale(1.15);
  }
  
  &.disabled {
    padding-right: 10px;
    opacity: 0.7;
    cursor: not-allowed;
  }
}



// 中奖结果
.prize-result {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 3000;
  display: flex;
  align-items: center;
  justify-content: center;
  // 背景图片, 两个图片拼接
  .result-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 408px;
    height: 92px;
    background: url('/img/page_icons/lottery_reward_bg.png'),url('/img/page_icons/lottery_reward_bg.png');
    background-size: 100%;
    background-position: top, bottom;
    background-repeat: no-repeat;
    
    .result-icon{
      width: 42px;
      // height: 78px;
    }
    
    .result-prize{
      font-size: 36px;
      font-weight: bold;
      color: #fff;
      margin-right: 10px;
    }
    
    .double-reward-btn {
      margin-top: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px 20px;
      background: linear-gradient(45deg, #ff6b35, #f7931e);
      border-radius: 25px;
      cursor: pointer;
      transition: transform 0.2s;
      
      &:active {
        transform: scale(0.95);
      }
      
      img {
        width: 24px;
        height: 24px;
        margin-right: 8px;
      }
      
      span {
        color: #fff;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
}

// 动画
.prize-fade-enter-active,
.prize-fade-leave-active {
  transition: all 0.3s ease;
}

.prize-fade-enter-from,
.prize-fade-leave-to {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8);
}

</style>