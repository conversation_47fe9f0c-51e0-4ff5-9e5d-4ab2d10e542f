<template>
  <div class="game-menu" v-if="visible">
    <div class="menu-content">
      <div class="close-button" @click="closeMenu">
        <div class="close-icon">✕</div>
      </div>
      
      <div class="menu-title">{{ t('gameMenu.title') }}</div>
      
      <div class="menu-list">
        
        <div class="menu-item" @click="openRanking">
          <div class="menu-icon">🏆</div>
          <div class="menu-text">{{ t('gameMenu.ranking') }}</div>
          <div class="arrow">→</div>
        </div>

        <div class="menu-item" @click="openCardView">
          <div class="menu-icon">🎴</div>
          <div class="menu-text">{{ t('gameMenu.cardView') }}</div>
          <div class="arrow">→</div>
        </div>

        <div class="menu-item" @click="openCollectionStats">
          <div class="menu-icon">📊</div>
          <div class="menu-text">{{ t('gameMenu.collectionStats') }}</div>
          <div class="arrow">→</div>
        </div>

        <!-- 测试功能 (开发期间) -->
        <div class="menu-item" @click="testBossDialogue">
          <div class="menu-icon">👹</div>
          <div class="menu-text">测试BOSS对话</div>
          <div class="arrow">→</div>
        </div>

        <div class="menu-item" @click="testMonumentQuiz">
          <div class="menu-icon">🏛️</div>
          <div class="menu-text">测试古迹问答</div>
          <div class="arrow">→</div>
        </div>

        <div class="menu-item" @click="simulateCollection">
          <div class="menu-icon">⚡</div>
          <div class="menu-text">模拟收集 (减血量)</div>
          <div class="arrow">→</div>
        </div>
        
        <!-- 分割线 -->
        <div class="menu-divider"></div>
        
        <!-- 音乐设置 -->
        <div class="menu-item toggle-item">
          <div class="menu-icon">🎵</div>
          <div class="menu-text">{{ t('gameMenu.music') }}</div>
          <van-switch 
            v-model="musicEnabled" 
            @change="onMusicToggle"
            size="24px" 
          />
        </div>
        
        <!-- 音效设置 -->
        <div class="menu-item toggle-item">
          <div class="menu-icon">🔊</div>
          <div class="menu-text">{{ t('gameMenu.soundEffects') }}</div>
          <van-switch 
            v-model="sfxEnabled" 
            @change="onSfxToggle"
            size="24px" 
          />
        </div>
        
        <!-- 震动设置 -->
        <div class="menu-item toggle-item">
          <div class="menu-icon">📳</div>
          <div class="menu-text">{{ t('gameMenu.vibration') }}</div>
          <van-switch 
            v-model="vibrationEnabled" 
            @change="onVibrationToggle"
            size="24px" 
          />
        </div>
        
        <!-- 语言设置 -->
        <div class="menu-item">
          <div class="menu-icon">🌐</div>
          <div class="menu-text">{{ t('gameMenu.language') }}</div>
          <LanguageSwitcher />
        </div>
        <!-- 隐藏游戏UI -->
        <div class="menu-item toggle-item">
          <div class="menu-icon">👀</div>
          <div class="menu-text">{{ t('gameMenu.hideUI') }}</div>
          <van-switch 
            v-model="hideUI" 
            @change="onHideUI"
            size="24px" 
          />
        </div>
        <!-- 分割线 -->
        <div class="menu-divider"></div>
        
        <div class="menu-item" @click="openContact">
          <div class="menu-icon">📞</div>
          <div class="menu-text">{{ t('gameMenu.contactUs') }}</div>
          <div class="arrow">→</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { showToast } from 'vant'
import audioManager from '@/utils/audio'
import LanguageSwitcher from '@/components/LanguageSwitcher.vue'

const { t } = useI18n()

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:show', 'openUpgrade', 'openRanking', 'openTasks', 'hideUI', 'openCardView', 'openCollectionStats', 'testBossDialogue', 'testMonumentQuiz', 'simulateCollection'])

const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 设置状态
const musicEnabled = ref(true)
const sfxEnabled = ref(true)
const vibrationEnabled = ref(true)

// 初始化设置
onMounted(() => {
  musicEnabled.value = audioManager.musicEnabled
  sfxEnabled.value = audioManager.sfxEnabled
  
  // 从localStorage读取震动设置
  const vibration = localStorage.getItem('gameVibrationEnabled')
  vibrationEnabled.value = vibration ? JSON.parse(vibration) : true
})
const hideUI = ref(false)
const onHideUI = (value) => {
  hideUI.value = value
  emit('hideUI', value)
}

const openRanking = () => {
  emit('openRanking')
  visible.value = false
}

const openCardView = () => {
  emit('openCardView')
  visible.value = false
}

const openCollectionStats = () => {
  emit('openCollectionStats')
  visible.value = false
}

const testBossDialogue = () => {
  emit('testBossDialogue')
  visible.value = false
}

const testMonumentQuiz = () => {
  emit('testMonumentQuiz')
  visible.value = false
}

const simulateCollection = () => {
  emit('simulateCollection')
  visible.value = false
}

const openContact = () => {
  showToast(t('gameMenu.contactDev'))
}

const closeMenu = () => {
  visible.value = false
}

// 音乐开关
const onMusicToggle = (value) => {
  audioManager.setMusicEnabled(value)
  showToast(value ? t('gameMenu.musicEnabled') : t('gameMenu.musicDisabled'))
}

// 音效开关
const onSfxToggle = (value) => {
  audioManager.setSfxEnabled(value)
  showToast(value ? t('gameMenu.sfxEnabled') : t('gameMenu.sfxDisabled'))
}

// 震动开关
const onVibrationToggle = (value) => {
  localStorage.setItem('gameVibrationEnabled', JSON.stringify(value))
  showToast(value ? t('gameMenu.vibrationEnabled') : t('gameMenu.vibrationDisabled'))
}
</script>

<style lang="scss" scoped>
.game-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  backdrop-filter: blur(10px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.menu-content {
  background: linear-gradient(145deg, rgba(176, 147, 147, 0.8), rgba(122, 89, 89, 0.5));
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  padding: 40px 20px 20px;
  min-width: 85%;
  text-align: center;
  position: relative;
  max-height: 80vh;
  overflow-y: auto;
}

.close-button {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  .close-icon{
    font-size: $font-sm;
    font-weight: bold;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &:hover {
    background: rgba(135, 118, 118, 0.2);
    border-color: rgba(183, 165, 165, 0.5);
    transform: scale(1.1);
  }
}

.menu-title {
  font-size: $font-base;
  color: white;
  font-weight: bold;
  margin-bottom: 30px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.menu-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.menu-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.2);
  margin: 10px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(.toggle-item) {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }

  .menu-icon {
    width: 48px;
    height: 48px;
    flex-shrink: 0;
    font-size: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .menu-text {
    font-size: $font-sm;
    font-weight: 500;
    flex: 1;
    text-align: left;
  }

  .arrow {
    margin-left: auto;
    width: 16px;
    height: 16px;
    opacity: 0.7;
    transition: transform 0.3s ease;
    font-size: $font-sm;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &:hover .arrow {
    transform: translateX(3px);
  }

  &.toggle-item {
    cursor: default;
    
    .arrow {
      display: none;
    }
  }
}

:deep(.van-switch) {
  --van-switch-on-background: linear-gradient(45deg, #4CAF50, #45a049);
}

:deep(.language-switcher) {
  .van-popover__wrapper{
    display: flex;
  }
  .language-button {
    font-size: 14px;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    
    img {
      height: 20px;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .menu-content {
    margin: 0 20px;
    padding: 30px 15px 15px;
    min-width: 70%;
  }

  .menu-title {
    // font-size: 20px;
    margin-bottom: 25px;
  }

  .menu-item {
    padding: 10px 15px;

    .menu-icon {
      // width: 20px;
      // height: 20px;
      // font-size: 16px;
    }

    .menu-text {
      // font-size: 14px;
    }

    .arrow {
      width: 14px;
      height: 14px;
      font-size: 12px;
    }
  }
}

@media (max-width: 480px) {
  .menu-content {
    margin: 0 15px;
    padding: 25px 12px 12px;
    min-width: 70%;
  }

  .menu-title {
    // font-size: 18px;
    margin-bottom: 20px;
  }

  .menu-list {
    gap: 12px;
  }

  .menu-item {
    padding: 8px 12px;
    
    .menu-text {
      // font-size: 12px;
    }
  }
}
</style> 