<template>
  <van-popup
    v-model:show="visible"
    position="center"
    closeable
    class="city-collection-popup"
    :close-icon="'/img/page_icons/close-btn.png'"
    close-icon-position="top-right"
    round
  >
    <div class="city-collection-container">
      <div class="collection-header">
        {{ t('cityCollections.title') }}
      </div>

      <div class="city-selector">
        <!-- <div class="city-indicators-container">
          <van-swipe
            v-model="activeCityIndex"
            @change="onCityChange"
            :show-indicators="false"
            class="city-indicators-swiper"
            ref="cityIndicatorSwiper"
          >
            <van-swipe-item
              v-for="city in cities"
              :key="city.id"
              class="city-indicator-slide"
            >
              <div class="cities-preview">
                <div
                  v-if="getPrevCity(activeCityIndex)"
                  class="city-indicator side-city left-city"
                  @click="goToCity(getPrevCityIndex(activeCityIndex))"
                >
                  <img :src="getPrevCity(activeCityIndex).image" :alt="getPrevCity(activeCityIndex).name" class="city-image" />
                  <div class="city-name">{{ getPrevCity(activeCityIndex).name }}</div>
                </div>

                <div class="city-indicator active-city">
                  <img :src="currentCity.image" :alt="currentCity.name" class="city-image" />
                  <div class="city-name">{{ currentCity.name }}</div>
                </div>

                <div
                  v-if="getNextCity(activeCityIndex)"
                  class="city-indicator side-city right-city"
                  @click="goToCity(getNextCityIndex(activeCityIndex))"
                >
                  <img :src="getNextCity(activeCityIndex).image" :alt="getNextCity(activeCityIndex).name" class="city-image" />
                  <div class="city-name">{{ getNextCity(activeCityIndex).name }}</div>
                </div>
              </div>
            </van-swipe-item>
          </van-swipe>
        </div> -->

        <!-- <div class="guard-info-section">
          <div class="resource-stats">
            <div class="resource-item">
              <img src="/img/page_icons/Coin.png" class="resource-icon" />
              <span class="resource-value">+ {{ currentCityGuard.goldPerHour }}/h</span>
            </div>
            <div class="resource-item">
              <img src="/img/page_icons/diamond.png" class="resource-icon" />
              <span class="resource-value">+ {{ currentCityGuard.diamondPerHour }}/h</span>
            </div>
          </div>

          <div class="guard-avatar-container">
            <div class="guard-avatar">
              <img :src="authStore.user.avatar_url" class="guard-image" />
              <div class="guard-level">{{ currentCityGuard.level }}</div>
            </div>
          </div>

          <div class="guard-info">
            <div class="guard-name">{{ authStore.user.nickname }}</div>
            <div style="display: flex;justify-content: center;align-items: center;">

              <div class="guard-title">升级</div>
              <div class="guard-exp-bar">
                <div class="exp-fill" :style="{ width: '60%' }"></div>
              </div>
            </div>
            <div class="passive-income-button" @click="openPassiveIncome">
                <div class="notification-badge" v-if="hasPassiveIncome"></div>
                <div class="item-name">{{ t('passiveIncome.title') }}</div>
            </div>
          </div>
        </div>
        <div class="passive-income-button" @click="openPassiveIncome"></div>
        <div class="guard-title-name">
          {{ currentCityGuard.title }}
        </div> -->

        <div class="city-collections">
          <div class='description'>
            <!-- {{t('cityCollections.description')}} -->
          </div>
          <!-- 收藏品网格 -->
          <div class="collections-grid">
            <!-- 加载状态 -->
            <div v-if="loading" class="loading-state">
              <van-loading size="24px" color="#FFD700">加载中...</van-loading>
            </div>

            <!-- 收藏品列表 -->
            <div
              v-for="item in currentCityItems"
              :key="item.item_id"
              class="collection-item"
              :class="{
                rare: item.rarity === 'rare',
                epic: item.rarity === 'epic',
                collected: item.owned,
                locked: !item.owned,
                flipping: item.isFlipping
              }"
              @click="showItemDetail(item)"
            >
              <div class="card-container" :class="{ 'flip-animation': item.isFlipping }">
                <div class="card-front">
                  <div class="item-frame" :class="getRarityClass(item.rarity)">
                    <img
                      v-if="item.owned && item.image_url"
                      :src="item.image_url"
                      :alt="item.name"
                      class="item-image"
                    />
                    <div v-else class="item-locked">
                      <van-icon name="question-o" size="24" />
                    </div>
                  </div>
                  <div class="item-info">
                    {{ item.owned ? item.name : 'The Last Judgment' }}
                  </div>
                </div>
                <div class="card-back">
                  <div class="card-back-content">
                    <van-icon name="question-o" size="48" />
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="!loading && currentCityItems?.length === 0" class="empty-state">
              <van-icon name="photo-o" size="48" />
              <p>暂无收藏品数据</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </van-popup>

  <!-- 卡片详情弹窗 -->
  <van-popup
  v-if="selectedCard"
    v-model:show="showCardDetailDialog"
    position="center"
    closeable
    :close-icon="''"
    :overlay-style="{
      'background-image': `url(${selectedCard.rarity == 'rare'?'/img/page_icons/card_detail_bg_fx_rare.png':(selectedCard.rarity == 'epic'?'img/page_icons/card_detail_bg_fx_epic.png':'/img/page_icons/card_detail_bg_fx_common.png')}), url(${selectedCard.rarity == 'rare'?'img/page_icons/card_details_bg_fxb_rare.png':selectedCard.rarity == 'epic'?'img/page_icons/card_details_bg_fxb_epic.png':'/img/page_icons/card_details_bg_fxb_common.png'})!important`,
      'background-position': 'top center, bottom center!important',
      'background-color': 'rgb(0, 0, 0)!important',
      'background-repeat': 'no-repeat, no-repeat!important',
      'background-size': '408px auto, 412px auto!important'
    }"
    round
    class="card-detail-popup"
  >
    <div class="card-detail-container" :class="selectedCard.rarity == 'rare'?'rare':(selectedCard.rarity == 'epic'?'epic':'')" >
      

      <div class="card-detail-image">
        <div class="exp">
          <p class="exp-num">
            {{ selectedCard.exp }}
          </p>
          <p class="exp-text">{{t('game.exp')}}</p>
        </div>
        <img :src="selectedCard.image_url" :alt="selectedCard.name" />
        <div class="card-detail-header">
          <h3>{{ selectedCard.name }}</h3>
        </div>
      </div>
      <div class="card-detail-info">
          <p>{{ selectedCard.description || '这是一张珍贵的收藏卡片，记录着城市的历史与文化。' }}</p>

      </div>
    </div>
  </van-popup>
</template>
<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { showToast, showFailToast } from 'vant'
import api from '@/utils/api'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
const authStore = useAuthStore()
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:show', 'city-selected', 'openPassiveIncome', 'openMonumentQuiz'])

const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})
const { t } = useI18n()
// 响应式数据
const loading = ref(false)
const activeCityIndex = ref(0)
const cityCollections = ref({}) // 存储各城市的收藏品数据
const cityIndicatorSwiper = ref(null) // 城市指示器swiper引用
const showCardDetailDialog = ref(false) // 卡片详情弹窗显示状态
const selectedCard = ref(null) // 选中的卡片
const hasPassiveIncome = ref(false)

// 城市列表
const cities = ref([
  {
    id: 'vatican',
    name: '梵蒂冈',
    image: '/img/page_icons/vatican_cities.png',
    guard: {
      name: 'ZHOURUNFA.LI',
      title: '文化守卫',
      avatar: '/img/page_icons/vatican_cities.png', // 临时使用城市图片作为头像
      level: 6,
      expPerHour: 150
    },
    items: [
      {
        id: 'vatican_card_1',
        rarity: 'common',
        name: 'The Last Judgment',
        owned: true,
        image_url: '/img/page_icons/vatican_card1.png',
        exp: 25,
        isFlipping: false
      },
      {
        id: 'vatican_card_2',
        rarity: 'rare',
        name: 'The Last Judgment',
        owned: true,
        exp: 50,
        image_url: '/img/page_icons/vatican_card1.png',
        isFlipping: false
      },
      {
        id: 'vatican_card_3',
        rarity: 'epic',
        name: 'The Last Judgment',
        owned: true,
        exp: 100,
        image_url: '/img/page_icons/vatican_card1.png',
        isFlipping: false
      },
      {
        id: 'vatican_card_4',
        rarity: 'epic',
        name: 'The Last Judgment',
        owned: true,
        exp: 100,
        image_url: '/img/page_icons/vatican_card1.png',
        isFlipping: false
      },
      {
        id: 'vatican_card_5',
        rarity: 'epic',
        name: 'The Last Judgment',
        owned: true,
        exp: 100,
        image_url: '/img/page_icons/vatican_card1.png',
        isFlipping: false
      },
      {
        id: 'vatican_card_6',
        rarity: 'rare',
        name: 'The Last Judgment',
        owned: true,
        exp: 50,
        image_url: '/img/page_icons/vatican_card1.png',
        isFlipping: false
      },
      {
        id: 'vatican_card_7',
        rarity: 'rare',
        name: 'The Last Judgment',
        owned: true,
        exp: 50,
        image_url: '/img/page_icons/vatican_card1.png',
        isFlipping: false
      },
      {
        id: 'vatican_card_8',
        rarity: 'epic',
        name: 'The Last Judgment',
        owned: true,
          exp: 100,
        image_url: '/img/page_icons/vatican_card1.png',
        isFlipping: false
      },
      {
        id: 'vatican_card_9',
        rarity: 'rare',
        name: 'The Last Judgment',
        owned: true,
        exp: 50,
        image_url: '/img/page_icons/vatican_card1.png',
        isFlipping: false
      },
      {
        id: 'vatican_card_10',
        rarity: 'rare',
        exp: 50,
        name: 'The Last Judgment',
        owned: true,
        image_url: '/img/page_icons/vatican_card1.png',
        isFlipping: false
      },
      {
        id: 'vatican_card_11',
        rarity: 'rare',
        name: 'The Last Judgment',
        owned: true,
        exp: 50,
        image_url: '/img/page_icons/vatican_card1.png',
        isFlipping: false
      },
      {
        id: 'vatican_card_12',
        rarity: 'epic',
        name: 'The Last Judgment',
        owned: true,
        exp: 100,
        image_url: '/img/page_icons/vatican_card1.png',
        isFlipping: false
      }
    ]
  },
  {
    id: 'shanghai',
    name: '上海',
    image: '/img/page_icons/default_cities.png',
    guard: {
      name: '东方守护者',
      avatar: '/img/page_icons/default_cities.png',
      level: 3,
      expPerHour: 120
    },
    items: []
  },
  {
    id: 'guangzhou',
    name: '广州',
    image: '/img/page_icons/default_cities.png',
    guard: {
      name: '南国卫士',
      avatar: '/img/page_icons/default_cities.png',
      level: 2,
      expPerHour: 80
    },
    items: []
  },
  {
    id: 'shenzhen',
    name: '深圳',
    image: '/img/page_icons/vatican_cities.png',
    guard: {
      name: '科技先锋',
      avatar: '/img/page_icons/vatican_cities.png',
      level: 4,
      expPerHour: 140
    },
    items: []
  }
])

// 计算属性
const currentCity = computed(() => {
  return cities.value[activeCityIndex.value] || cities.value[0]
})

const currentCityGuard = computed(() => {
  const city = currentCity.value
  return city?.guard || {
    name: '未知守卫',
    avatar: '/img/page_icons/default_cities.png',
    level: 1,
    goldPerHour: 50,
    diamondPerHour: 1
  }
})

const currentCityItems = computed(() => {
  const city = currentCity.value
  return city?.items || []
})

// 方法
const loadCityCollections = async (cityId) => {
  if (cityCollections.value[cityId]) {
    return // 已加载过
  }

  try {
    loading.value = true
    // 文化图鉴现在通过问答系统获得，这里显示模拟数据
    // 实际的图鉴收集通过文化问答系统完成
    cityCollections.value[cityId] = {
      items: [],
      totalItems: 0,
      collectedCount: 0,
      completionRate: 0
    }
    checkPassiveIncome()
  } catch (error) {
    console.error(`加载城市 ${cityId} 收藏品失败:`, error)
    showFailToast('加载收藏品失败')
  } finally {
    loading.value = false
  }
}

// 检查是否有可领取的被动收益
const checkPassiveIncome = async () => {
  try {
    const response = await api.passiveIncome.getStatus()
    if (response.data.success) {
      hasPassiveIncome.value = response.data.data.can_collect
    }
  } catch (error) {
    console.error('检查被动收益状态失败:', error)
  }
}

const openPassiveIncome = () => {
  emit('openPassiveIncome')
}


const getRarityClass = (rarity) => {
  return `rarity-${rarity}`
}

const getExpPercentage = (item) => {
  if (!item.maxExp || item.maxExp === 0) return 0
  return Math.min(100, (item.currentExp || 0) / item.maxExp * 100)
}

const showItemDetail = (item) => {
  if (!item.owned) {
    showToast('尚未收集此物品')
    return
  }

  // 触发翻牌效果
  item.isFlipping = true

  // 延迟显示详情，等待翻牌动画
  setTimeout(() => {
    item.isFlipping = false
    showCardDetail(item)
  }, 600)
}

// 显示卡片详情弹窗
const showCardDetail = (item) => {
  selectedCard.value = item
  // showCardDetailDialog.value = true

  emit('openMonumentQuiz', item.id)
}

const onCityChange = (index) => {
  activeCityIndex.value = index
  const city = cities.value[index]
  if (city) {
    loadCityCollections(city.id)
    emit('city-selected', city.id)
  }
}

// 跳转到指定城市
const goToCity = (index) => {
  if (cityIndicatorSwiper.value) {
    cityIndicatorSwiper.value.swipeTo(index)
  }
}

// 获取前一个城市
const getPrevCity = (currentIndex) => {
  const prevIndex = currentIndex - 1
  return prevIndex >= 0 ? cities.value[prevIndex] : null
}

// 获取后一个城市
const getNextCity = (currentIndex) => {
  const nextIndex = currentIndex + 1
  return nextIndex < cities.value.length ? cities.value[nextIndex] : null
}

// 获取前一个城市的索引
const getPrevCityIndex = (currentIndex) => {
  return currentIndex - 1
}

// 获取后一个城市的索引
const getNextCityIndex = (currentIndex) => {
  return currentIndex + 1
}

// 监听弹窗显示状态
watch(() => props.show, (newVal) => {
  if (newVal) {
    // 加载当前城市的收藏品
    const currentCityId = currentCity.value.id
    loadCityCollections(currentCityId)
  }
})

// 组件挂载时加载默认城市
onMounted(() => {
  if (props.show) {
    loadCityCollections(currentCity.value.id)
  }
})
</script>

<style lang="scss" scoped>
.city-collection-popup{
  
  overflow-y: visible;
  :deep(.van-popup__close-icon--top-right){
  top: -16px!important;
  right: -16px!important;
  .van-icon__image{
      width: 66px;
      height: 70px;

    }
  
}
}
.city-collection-container {
  // padding: 20px;
  background: url('/img/page_icons/cities_collections_bg.png') no-repeat center center;
  border: 4px solid #616881;
  border-radius: 10px;
  color: white;
  width: 704px;
  height: 1220px;
  display: flex;
  flex-direction: column;
  
}

.collection-header {
  background: url('/img/page_icons/cities_card_title.png') no-repeat center center;
  background-size: 100% 100%;
  width: 556px;
  height: 102px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: bold;
  margin-top: -20px;
}



.city-selector {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.city-indicators-container {
  // height: 200px;
  // background: rgba(0, 0, 0, 0.3);
  // backdrop-filter: blur(10px);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  margin-top: 20px;
  :deep(.van-swipe__track) {
    padding: 20px!important;
  }
}

.city-indicators-swiper {
  height: 100%;
}

.city-indicator-slide {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cities-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
}

.city-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  position: relative;
}

.active-city {
  z-index: 3;
  position: relative;
  flex: 0 0 auto;
  order: 2;

  img {
    width: 460px;
    height: 268px;
    border: 10px solid white;
    border-radius: 20px;
  }

  .city-name {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: 28px;
    font-weight: bold;
    width: 269px;
    height: 67px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url('/img/page_icons/collections_city_name_bg.png') no-repeat center center;
    background-size: 100% 100%;
  }
}

.side-city {
  position: relative;
  z-index: 1;
  opacity: 0.7;
  transform: scale(0.8);
  flex: 0 0 auto;

  img {
    width: 180px;
    height: 105px;
    border: 3px solid white;
    border-radius: 8px;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.8);
    filter: brightness(0.8);
  }

  .city-name {
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: 14px;
    font-weight: bold;
    background: rgba(128, 128, 128, 0.8);
    padding: 4px 8px;
    border-radius: 4px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  }

  &:hover {
    opacity: 0.9;
    transform: scale(0.85);
  }
}

.left-city {
  order: 1;
}

.right-city {
  order: 3;
}

// 守卫信息区域样式
.guard-info-section {
  margin: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.guard-title-name{
  width: 204px;
  height: 46px;
  background: url('/img/page_icons/guard_name_capture.png') no-repeat center center;
  background-size: 100% 100%;
  color: #fff;
  font-size: 30px;
  font-weight: bold;
  text-shadow: #3B3B3B 1px 1px 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px auto;;
}
.resource-stats {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.resource-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.resource-icon {
  width: 30px;
  height: 30px;
}

.resource-value {
  color: white;
  font-size: 30px;
  font-family: 'Arial', sans-serif;
  font-weight: bold;
}

.guard-avatar-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.guard-avatar {
  position: relative;
  width: 184px;
  height: 184px;
  border-radius: 50%;
  background: url('/img/page_icons/collection_user_bg.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.guard-image {
  width: 59%;
  height: 59%;
  border-radius: 50%;
  object-fit: cover;
}

.guard-level {
  position: absolute;
  bottom: 25px;
  left: 50%;
  transform: translate(-50%);
  background: url('/img/page_icons/collection_level_bg.png') no-repeat center center;
  background-size: 100% 100%;
  color: #fff;
  width: 58px;
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26px;
  font-weight: bold;
}

.guard-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: right;
}

.guard-name {
  color: white;
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 4px;
  width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.guard-title {
  // color: #FFD700;
  font-size: 30px;
  margin-bottom: 8px;
}

.guard-exp-bar {
  width: 120px;
  height: 8px;
  background: rgba(0, 20, 27, 0.9);
  border-radius: 4px;
  overflow: hidden;
}

.exp-fill {
  height: 100%;
  background: rgb(79, 234, 79);
  border-radius: 4px;
  transition: width 0.3s ease;
}


.passive-income-button{
  position: relative;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  .item-name{
    color: #fff;
    font-size: 30px;
  }
  .notification-badge {
    position: absolute;
    top: -12px;
    right: -16px;
    width: 30px;
    height: 30px;
    background: url(/img/page_icons/Battle-AlertIcon.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
  }
}
.city-collections {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.city-info {
  margin-bottom: 20px;
}

.city-banner {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  height: 120px;
}

.city-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.city-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 15px;
}

.city-name {
  font-size: 18px;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 8px;
}

.collection-progress {
  .progress-text {
    font-size: 12px;
    color: #ccc;
    margin-bottom: 5px;
    display: block;
  }
}

.collections-grid {
  // flex: 1;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 10px;
  padding: 20px;
  overflow-y: auto;
}

.collection-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  perspective: 1000px;
  width: 140px;
  height: 176px;
  background: url('/img/page_icons/collection_item_bg.png') no-repeat center center;
  background-size: 100% 100%;

  &:hover {
    transform: translateY(-2px);
  }
// 卡片翻转容器
.card-container {
    position: relative;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    transition: transform 0.6s;

    &.flip-animation {
      transform: rotateY(180deg);
    }
  }

  .card-front,
  .card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .card-front {
    z-index: 2;
  }

  .card-back {
    transform: rotateY(180deg);
    // background: linear-gradient(135deg, #6e7593 0%, #4c43c6 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }
  &.rare {
    background: url('/img/page_icons/collection_item_bg_rare.png') no-repeat center center;
    background-size: 100% 100%;
    .card-back{
      // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  }
  &.epic {
    background: url('/img/page_icons/collection_item_bg_epic.png') no-repeat center center;
    background-size: 100% 100%;
    .card-back{
      // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  }

  &.locked {
    .item-info {
      opacity: 0.6;
    }
  }
}



.card-back-content {
  text-align: center;
}

.item-frame {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 98px;
  height: 98px;
  border-radius: 50%;
  transition: all 0.3s ease;
  margin-top: 20px;
  overflow: hidden;
  background: url('/img/page_icons/collection_item_img_bg.png') no-repeat center center;
  background-size: 100% 100%;

  // 稀有度边框样式
  &.rarity-common {
    // border-color: #9e9e9e;
    // box-shadow: 0 0 10px rgba(158, 158, 158, 0.5);
  }

  &.rarity-rare {
    // border-color: #2196f3;
    // box-shadow: 0 0 10px rgba(33, 150, 243, 0.5);
  }

  &.rarity-epic {
    // border-color: #9c27b0;
    // box-shadow: 0 0 10px rgba(156, 39, 176, 0.5);
     animation: rarity-glow 2s ease-in-out infinite alternate;
  }

}

@keyframes rarity-glow {
  from {
    box-shadow: 0 0 15px rgba(255, 152, 0, 0.7);
  }
  to {
    box-shadow: 0 0 25px rgba(255, 152, 0, 1);
  }
}

.item-image {
  width: 86%;
  height: 86%;
  object-fit: cover;
  border-radius: 50%;
}

.item-locked {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}

.item-info {
  position: absolute;
  width: 100%;
  height: 100%;
  padding: 10px;
  padding-top: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 26px;
}




.loading-state, .empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px 20px;
  color: #ccc;

  .van-icon {
    margin-bottom: 10px;
  }
}

// 卡片详情弹窗样式
.card-detail-popup {
  background: none;
  // :deep(.van-popup) {
  //   background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  //   border: 3px solid #FFD700;
  //   max-width: 400px;
  //   width: 90%;
  // }
}

.card-detail-container {
  padding: 50px;
  width: 692px;
  height: 928px;
  background: url('/img/page_icons/common_card_details_bg.png') no-repeat center center;
  background-size: 100% 100%;;
   .card-detail-image {
    position: relative;
    width: 100%;
    height: 60%;
      .exp{
          position: absolute;
          top: -2px;
          left: 0;
          width: 80px;
          height: 140px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background: #fff;
          border-top-left-radius: 20px;
          border-bottom-right-radius: 40px;
          background-color: rgb(172, 172, 171);
          color: #fff;
          font-size: 24px;
          font-weight: bold;
          .exp-num{

          }
      }
      .card-detail-header {
        position: absolute;
        left: 0;
        bottom: 0;
        background: #8F8F8E;
        padding: 10px;
        border-top-right-radius: 20px;
        color: #fff;
        h3 {
          font-size: 24px;
        }

      }
      img {
        width: 100%;
        height: 100%;
        object-fit: fill;
      }
  }

  .card-detail-info {
    background-color: rgb(215, 215, 215);
    color: #313131;
    padding: 10px;
    border-bottom-right-radius: 24px;
    border-bottom-left-radius: 24px;
    height: 40%;
    overflow-y: auto;
      p {
        margin: 0;
        line-height: 1.5;
      }
  
  }
  &.rare {
    background: url('/img/page_icons/rare_card_details_bg.png') no-repeat center center;
    background-size: 100% 100%;
    .card-detail-header, .exp {
        background-color: rgb(56, 87, 180);
    }
    .card-detail-info {
      background-color: rgb(153, 188, 236);
    }
  }
  &.epic {
    background: url('/img/page_icons/epic_card_details_bg.png') no-repeat center center;
    background-size: 100% 100%;
    .card-detail-header, .exp{
      background-color: rgb(153, 56, 180);
    }
    .card-detail-info {
      background-color: rgb(214, 153, 236);
    }
  }
 
}





</style>