<template>
  <div class="test-page">
    <h1>古迹问答界面测试</h1>
    <button @click="showQuiz = true" class="test-btn">显示问答界面</button>
    
    <MonumentQuiz 
      :show="showQuiz"
      :monumentId="'monument_1'"
      @close="showQuiz = false"
      @success="onSuccess"
      @failure="onFailure"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import MonumentQuiz from '@/components/game/MonumentQuiz.vue'

const showQuiz = ref(false)

const onSuccess = (result) => {
  console.log('问答成功:', result)
}

const onFailure = (result) => {
  console.log('问答失败:', result)
}
</script>

<style scoped>
.test-page {
  padding: 20px;
  text-align: center;
}

.test-btn {
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  margin: 20px;
}

.test-btn:hover {
  background: #0056b3;
}
</style>
